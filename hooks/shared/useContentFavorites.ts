import { useFavorites, UseFavoritesOptions } from './useFavorites';

/**
 * Hook for managing meal favorites
 */
export const useMealFavorites = (options?: Partial<Omit<UseFavoritesOptions, 'contentType'>>) => {
  return useFavorites({
    contentType: 'meal',
    ...options,
  });
};

/**
 * Hook for managing date night favorites
 */
export const useDateNightFavorites = (options?: Partial<Omit<UseFavoritesOptions, 'contentType'>>) => {
  return useFavorites({
    contentType: 'date_night',
    ...options,
  });
};

/**
 * Hook for managing memory favorites (scrapbook)
 */
export const useMemoryFavorites = (options?: Partial<Omit<UseFavoritesOptions, 'contentType'>>) => {
  return useFavorites({
    contentType: 'memory',
    onSuccess: (itemId, isFavorited) => {
      // Custom success handling for memories
      console.log(`Memory ${itemId} ${isFavorited ? 'liked' : 'unliked'}`);
      options?.onSuccess?.(itemId, isFavorited);
    },
    ...options,
  });
};

/**
 * Hook for managing couple profile saves/bookmarks
 */
export const useCoupleProfileSaves = (options?: Partial<Omit<UseFavoritesOptions, 'contentType'>>) => {
  return useFavorites({
    contentType: 'couple_profile',
    onSuccess: (itemId, isFavorited) => {
      // Custom success handling for couple profiles
      console.log(`Couple profile ${itemId} ${isFavorited ? 'saved' : 'unsaved'}`);
      options?.onSuccess?.(itemId, isFavorited);
    },
    ...options,
  });
};

/**
 * Hook for managing playlist song favorites
 */
export const usePlaylistSongFavorites = (options?: Partial<Omit<UseFavoritesOptions, 'contentType'>>) => {
  return useFavorites({
    contentType: 'playlist_song',
    onSuccess: (itemId, isFavorited) => {
      // Custom success handling for playlist songs
      console.log(`Playlist song ${itemId} ${isFavorited ? 'favorited' : 'unfavorited'}`);
      options?.onSuccess?.(itemId, isFavorited);
    },
    ...options,
  });
};

// Re-export the core hook and types for convenience
export { useFavorites } from './useFavorites';
export type { UseFavoritesOptions, UseFavoritesReturn } from './useFavorites';

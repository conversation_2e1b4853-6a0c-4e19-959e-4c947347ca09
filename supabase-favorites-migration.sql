-- Migration script to update existing favorites table
-- Run this in your Supabase SQL Editor to update the existing table

-- Check current table structure
SELECT
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'favorites' AND table_schema = 'public'
ORDER BY ordinal_position;

-- Metadata column already exists, so we'll skip that step

-- Check current type constraint
SELECT
    conname as constraint_name,
    pg_get_constraintdef(c.oid) as constraint_definition
FROM pg_constraint c
JOIN pg_class t ON c.conrelid = t.oid
JOIN pg_namespace n ON t.relnamespace = n.oid
WHERE t.relname = 'favorites' AND n.nspname = 'public' AND c.contype = 'c';

-- Update the type constraint to include new content types
-- First, let's see what the current constraint allows
DO $$
BEGIN
    -- Drop existing type constraint if it exists
    IF EXISTS (
        SELECT 1 FROM pg_constraint c
        JOIN pg_class t ON c.conrelid = t.oid
        JOIN pg_namespace n ON t.relnamespace = n.oid
        WHERE t.relname = 'favorites' AND n.nspname = 'public'
        AND c.contype = 'c' AND c.conname LIKE '%type%'
    ) THEN
        ALTER TABLE public.favorites DROP CONSTRAINT IF EXISTS favorites_type_check;
        ALTER TABLE public.favorites DROP CONSTRAINT IF EXISTS favorites_type_check1;
        ALTER TABLE public.favorites DROP CONSTRAINT IF EXISTS favorites_type_check2;
    END IF;

    -- Add the new comprehensive type constraint
    ALTER TABLE public.favorites ADD CONSTRAINT favorites_type_check
        CHECK (type IN ('date_night', 'meal', 'memory', 'couple_profile', 'playlist_song'));
END $$;

-- Create new indexes if they don't exist
CREATE INDEX IF NOT EXISTS idx_favorites_metadata ON favorites USING GIN (metadata);

-- Update existing records to have empty metadata if null
UPDATE favorites SET metadata = '{}' WHERE metadata IS NULL;

-- Add comments for documentation
COMMENT ON TABLE favorites IS 'Stores user favorites across different content types';
COMMENT ON COLUMN favorites.type IS 'Content type: date_night, meal, memory, couple_profile, playlist_song';
COMMENT ON COLUMN favorites.metadata IS 'Additional context data stored as JSON (tags, notes, etc.)';
COMMENT ON COLUMN favorites.item_id IS 'ID of the favorited item (can be any string identifier)';

-- Test the new constraint by trying to insert a new content type
-- This should succeed
INSERT INTO public.favorites (user_id, item_id, type, metadata)
VALUES (
    auth.uid(),
    'test-memory-' || extract(epoch from now()),
    'memory',
    '{"test": true, "migrated_at": "' || now() || '"}'::jsonb
) ON CONFLICT (user_id, item_id, type) DO NOTHING;

-- Verify the migration results
SELECT
    'Migration completed successfully!' as status,
    count(*) as total_favorites,
    count(DISTINCT type) as content_types_count,
    array_agg(DISTINCT type) as supported_types
FROM public.favorites;

-- Show final table structure
SELECT
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'favorites' AND table_schema = 'public'
ORDER BY ordinal_position;

-- Show current constraints
SELECT
    conname as constraint_name,
    pg_get_constraintdef(c.oid) as constraint_definition
FROM pg_constraint c
JOIN pg_class t ON c.conrelid = t.oid
JOIN pg_namespace n ON t.relnamespace = n.oid
WHERE t.relname = 'favorites' AND n.nspname = 'public';

-- ============================================================================
-- MILESTONE TEMPLATES DATA
-- Comprehensive relationship milestone definitions as data (not schema)
-- ============================================================================

-- ============================================================================
-- RELATIONSHIP FOUNDATION MILESTONES
-- ============================================================================

INSERT INTO milestone_templates (milestone_key, category, title, description, field_schema, ui_config, display_order, is_core_milestone) VALUES

-- How We Met
('how_we_met', 'foundation', 'How We Met', 'The story of your first encounter', 
jsonb_build_object(
  'fields', jsonb_build_array(
    jsonb_build_object('key', 'meeting_context', 'type', 'select', 'label', 'How did you meet?', 'required', true,
      'options', jsonb_build_array('Dating app', 'Through friends', 'At work', 'At school', 'Chance encounter', 'Online community', 'Event/party', 'Other')),
    jsonb_build_object('key', 'meeting_date', 'type', 'date', 'label', 'When did you first meet?', 'required', true),
    jsonb_build_object('key', 'location', 'type', 'text', 'label', 'Where did you meet?', 'required', true),
    jsonb_build_object('key', 'story', 'type', 'textarea', 'label', 'Tell the story of how you met', 'required', true),
    jsonb_build_object('key', 'first_impressions', 'type', 'textarea', 'label', 'What were your first impressions of each other?'),
    jsonb_build_object('key', 'photos', 'type', 'photo_array', 'label', 'Photos from around that time'),
    jsonb_build_object('key', 'witnesses', 'type', 'text_array', 'label', 'Who else was there?'),
    jsonb_build_object('key', 'funny_details', 'type', 'textarea', 'label', 'Any funny or embarrassing details?')
  )
), jsonb_build_object('icon', '👫', 'color', '#FF6B6B'), 1, true),

-- First Move
('first_move', 'foundation', 'Who Made the First Move', 'The story of how your romantic connection began',
jsonb_build_object(
  'fields', jsonb_build_array(
    jsonb_build_object('key', 'who_made_move', 'type', 'select', 'label', 'Who made the first move?', 'required', true,
      'options', jsonb_build_array('Partner 1', 'Partner 2', 'Mutual')),
    jsonb_build_object('key', 'how_it_happened', 'type', 'select', 'label', 'How did it happen?', 'required', true,
      'options', jsonb_build_array('Asked out in person', 'Sent a message', 'Made a phone call', 'Through a friend', 'Social media', 'Other')),
    jsonb_build_object('key', 'date_of_move', 'type', 'date', 'label', 'When did this happen?'),
    jsonb_build_object('key', 'story', 'type', 'textarea', 'label', 'Tell the story', 'required', true),
    jsonb_build_object('key', 'response', 'type', 'textarea', 'label', 'How did the other person respond?'),
    jsonb_build_object('key', 'emotions', 'type', 'textarea', 'label', 'How did each of you feel?'),
    jsonb_build_object('key', 'quotes', 'type', 'textarea', 'label', 'Any memorable words exchanged?')
  )
), jsonb_build_object('icon', '💘', 'color', '#FF8E8E'), 2, true),

-- First Date
('first_date', 'foundation', 'Our First Date', 'The details of your very first date together',
jsonb_build_object(
  'fields', jsonb_build_array(
    jsonb_build_object('key', 'date', 'type', 'date', 'label', 'When was your first date?', 'required', true),
    jsonb_build_object('key', 'location', 'type', 'text', 'label', 'Where did you go?', 'required', true),
    jsonb_build_object('key', 'activity', 'type', 'text', 'label', 'What did you do?', 'required', true),
    jsonb_build_object('key', 'who_planned', 'type', 'select', 'label', 'Who planned the date?',
      'options', jsonb_build_array('Partner 1', 'Partner 2', 'Planned together')),
    jsonb_build_object('key', 'memorable_moments', 'type', 'textarea', 'label', 'Most memorable moments'),
    jsonb_build_object('key', 'first_impressions', 'type', 'textarea', 'label', 'First date impressions'),
    jsonb_build_object('key', 'photos', 'type', 'photo_array', 'label', 'Photos from the date'),
    jsonb_build_object('key', 'funny_moments', 'type', 'textarea', 'label', 'Funny or awkward moments'),
    jsonb_build_object('key', 'knew_there_would_be_second', 'type', 'boolean', 'label', 'Did you know there would be a second date?')
  )
), jsonb_build_object('icon', '🌹', 'color', '#FFB6C1'), 3, true),

-- Became Official
('became_official', 'foundation', 'When We Became Official', 'The relationship status conversation',
jsonb_build_object(
  'fields', jsonb_build_array(
    jsonb_build_object('key', 'official_date', 'type', 'date', 'label', 'When did you become official?', 'required', true),
    jsonb_build_object('key', 'who_brought_it_up', 'type', 'select', 'label', 'Who brought up the conversation?',
      'options', jsonb_build_array('Partner 1', 'Partner 2', 'Mutual discussion')),
    jsonb_build_object('key', 'location', 'type', 'text', 'label', 'Where did this conversation happen?'),
    jsonb_build_object('key', 'story', 'type', 'textarea', 'label', 'How did the conversation go?', 'required', true),
    jsonb_build_object('key', 'emotions', 'type', 'textarea', 'label', 'How did you both feel?'),
    jsonb_build_object('key', 'celebration', 'type', 'textarea', 'label', 'How did you celebrate?'),
    jsonb_build_object('key', 'photos', 'type', 'photo_array', 'label', 'Photos from around that time')
  )
), jsonb_build_object('icon', '💕', 'color', '#FF69B4'), 4, true),

-- First I Love You
('first_i_love_you', 'foundation', 'First "I Love You"', 'When love was first expressed',
jsonb_build_object(
  'fields', jsonb_build_array(
    jsonb_build_object('key', 'date', 'type', 'date', 'label', 'When was it first said?', 'required', true),
    jsonb_build_object('key', 'who_said_it_first', 'type', 'select', 'label', 'Who said it first?', 'required', true,
      'options', jsonb_build_array('Partner 1', 'Partner 2')),
    jsonb_build_object('key', 'location', 'type', 'text', 'label', 'Where were you?'),
    jsonb_build_object('key', 'circumstances', 'type', 'textarea', 'label', 'What were the circumstances?', 'required', true),
    jsonb_build_object('key', 'response', 'type', 'textarea', 'label', 'How did the other person respond?'),
    jsonb_build_object('key', 'emotions', 'type', 'textarea', 'label', 'How did you both feel?'),
    jsonb_build_object('key', 'was_it_planned', 'type', 'boolean', 'label', 'Was it planned or spontaneous?'),
    jsonb_build_object('key', 'photos', 'type', 'photo_array', 'label', 'Photos from that day')
  )
), jsonb_build_object('icon', '❤️', 'color', '#DC143C'), 5, true);

-- ============================================================================
-- GETTING TO KNOW EACH OTHER MILESTONES
-- ============================================================================

INSERT INTO milestone_templates (milestone_key, category, title, description, field_schema, ui_config, display_order, is_core_milestone) VALUES

-- Meeting Friends
('meeting_friends', 'getting_to_know', 'Meeting Each Others Friends', 'First introductions to friend groups',
jsonb_build_object(
  'fields', jsonb_build_array(
    jsonb_build_object('key', 'whose_friends_first', 'type', 'select', 'label', 'Whose friends did you meet first?',
      'options', jsonb_build_array('Partner 1 friends', 'Partner 2 friends', 'Met both groups same time')),
    jsonb_build_object('key', 'first_meeting_date', 'type', 'date', 'label', 'When did the first meeting happen?'),
    jsonb_build_object('key', 'occasion', 'type', 'text', 'label', 'What was the occasion?'),
    jsonb_build_object('key', 'location', 'type', 'text', 'label', 'Where did you meet?'),
    jsonb_build_object('key', 'story', 'type', 'textarea', 'label', 'How did it go?'),
    jsonb_build_object('key', 'friends_reaction', 'type', 'textarea', 'label', 'What did the friends think?'),
    jsonb_build_object('key', 'nervous_moments', 'type', 'textarea', 'label', 'Any nervous or funny moments?'),
    jsonb_build_object('key', 'photos', 'type', 'photo_array', 'label', 'Photos from meeting friends')
  )
), jsonb_build_object('icon', '👥', 'color', '#32CD32'), 6, false),

-- Meeting Parents
('meeting_parents', 'getting_to_know', 'Meeting the Parents', 'First family introductions',
jsonb_build_object(
  'fields', jsonb_build_array(
    jsonb_build_object('key', 'whose_parents_first', 'type', 'select', 'label', 'Whose parents did you meet first?',
      'options', jsonb_build_array('Partner 1 parents', 'Partner 2 parents', 'Both sets same time')),
    jsonb_build_object('key', 'meeting_date', 'type', 'date', 'label', 'When did you meet them?'),
    jsonb_build_object('key', 'occasion', 'type', 'text', 'label', 'What was the occasion?'),
    jsonb_build_object('key', 'location', 'type', 'text', 'label', 'Where did you meet?'),
    jsonb_build_object('key', 'preparation', 'type', 'textarea', 'label', 'How did you prepare for meeting them?'),
    jsonb_build_object('key', 'story', 'type', 'textarea', 'label', 'How did the meeting go?'),
    jsonb_build_object('key', 'parents_reaction', 'type', 'textarea', 'label', 'What did the parents think?'),
    jsonb_build_object('key', 'memorable_moments', 'type', 'textarea', 'label', 'Most memorable moments'),
    jsonb_build_object('key', 'photos', 'type', 'photo_array', 'label', 'Photos with the parents')
  )
), jsonb_build_object('icon', '👨‍👩‍👧‍👦', 'color', '#4169E1'), 7, false),

-- First Overnight Stay
('first_overnight', 'getting_to_know', 'First Overnight Stay', 'The first time staying over together',
jsonb_build_object(
  'fields', jsonb_build_array(
    jsonb_build_object('key', 'date', 'type', 'date', 'label', 'When was the first overnight stay?'),
    jsonb_build_object('key', 'whose_place', 'type', 'select', 'label', 'Whose place was it?',
      'options', jsonb_build_array('Partner 1 place', 'Partner 2 place', 'Hotel/other location')),
    jsonb_build_object('key', 'planned_or_spontaneous', 'type', 'select', 'label', 'Was it planned or spontaneous?',
      'options', jsonb_build_array('Planned', 'Spontaneous')),
    jsonb_build_object('key', 'circumstances', 'type', 'textarea', 'label', 'What led to staying over?'),
    jsonb_build_object('key', 'morning_after', 'type', 'textarea', 'label', 'How was the morning after?'),
    jsonb_build_object('key', 'cute_moments', 'type', 'textarea', 'label', 'Cute or funny moments'),
    jsonb_build_object('key', 'photos', 'type', 'photo_array', 'label', 'Photos from that time')
  )
), jsonb_build_object('icon', '🏠', 'color', '#DDA0DD'), 8, false),

-- First Trip Together
('first_trip', 'getting_to_know', 'First Trip Together', 'Your first vacation or getaway',
jsonb_build_object(
  'fields', jsonb_build_array(
    jsonb_build_object('key', 'trip_date', 'type', 'date', 'label', 'When did you take your first trip?'),
    jsonb_build_object('key', 'destination', 'type', 'text', 'label', 'Where did you go?', 'required', true),
    jsonb_build_object('key', 'trip_length', 'type', 'select', 'label', 'How long was the trip?',
      'options', jsonb_build_array('Weekend getaway', '3-4 days', 'A week', 'More than a week')),
    jsonb_build_object('key', 'who_planned', 'type', 'select', 'label', 'Who planned the trip?',
      'options', jsonb_build_array('Partner 1', 'Partner 2', 'Planned together')),
    jsonb_build_object('key', 'activities', 'type', 'textarea', 'label', 'What did you do on the trip?'),
    jsonb_build_object('key', 'best_moments', 'type', 'textarea', 'label', 'Best moments from the trip'),
    jsonb_build_object('key', 'challenges', 'type', 'textarea', 'label', 'Any challenges or funny mishaps?'),
    jsonb_build_object('key', 'photos', 'type', 'photo_array', 'label', 'Trip photos'),
    jsonb_build_object('key', 'learned_about_each_other', 'type', 'textarea', 'label', 'What did you learn about each other?')
  )
), jsonb_build_object('icon', '✈️', 'color', '#20B2AA'), 9, false),

-- First Big Fight
('first_big_fight', 'getting_to_know', 'First Big Fight & Resolution', 'How you worked through your first major conflict',
jsonb_build_object(
  'fields', jsonb_build_array(
    jsonb_build_object('key', 'fight_date', 'type', 'date', 'label', 'When did this happen?'),
    jsonb_build_object('key', 'what_about', 'type', 'textarea', 'label', 'What was the fight about?', 'required', true),
    jsonb_build_object('key', 'how_it_started', 'type', 'textarea', 'label', 'How did it escalate?'),
    jsonb_build_object('key', 'resolution_process', 'type', 'textarea', 'label', 'How did you work through it?', 'required', true),
    jsonb_build_object('key', 'who_apologized_first', 'type', 'select', 'label', 'Who made the first move to resolve it?',
      'options', jsonb_build_array('Partner 1', 'Partner 2', 'Both at the same time')),
    jsonb_build_object('key', 'lessons_learned', 'type', 'textarea', 'label', 'What did you learn about each other?'),
    jsonb_build_object('key', 'stronger_after', 'type', 'boolean', 'label', 'Did it make your relationship stronger?'),
    jsonb_build_object('key', 'communication_changes', 'type', 'textarea', 'label', 'How did it change how you communicate?')
  )
), jsonb_build_object('icon', '🤝', 'color', '#FF6347'), 10, false),

-- Moving In Together
('moving_in_together', 'getting_to_know', 'Moving In Together', 'The big step of living together',
jsonb_build_object(
  'fields', jsonb_build_array(
    jsonb_build_object('key', 'move_in_date', 'type', 'date', 'label', 'When did you move in together?', 'required', true),
    jsonb_build_object('key', 'whose_place', 'type', 'select', 'label', 'Where did you move in?',
      'options', jsonb_build_array('Partner 1 place', 'Partner 2 place', 'New place together')),
    jsonb_build_object('key', 'decision_story', 'type', 'textarea', 'label', 'How did you decide to move in together?', 'required', true),
    jsonb_build_object('key', 'preparation', 'type', 'textarea', 'label', 'How did you prepare for living together?'),
    jsonb_build_object('key', 'first_week', 'type', 'textarea', 'label', 'How was the first week?'),
    jsonb_build_object('key', 'adjustments', 'type', 'textarea', 'label', 'What adjustments did you have to make?'),
    jsonb_build_object('key', 'favorite_things', 'type', 'textarea', 'label', 'Favorite things about living together'),
    jsonb_build_object('key', 'photos', 'type', 'photo_array', 'label', 'Photos of your place together')
  )
), jsonb_build_object('icon', '🏡', 'color', '#228B22'), 11, false);

-- ============================================================================
-- DEEPER COMMITMENT MARKERS
-- ============================================================================

INSERT INTO milestone_templates (milestone_key, category, title, description, field_schema, ui_config, display_order, is_core_milestone) VALUES

-- First Major Purchase
('first_major_purchase', 'commitment', 'First Major Purchase Together', 'Your first big financial decision as a couple',
jsonb_build_object(
  'fields', jsonb_build_array(
    jsonb_build_object('key', 'purchase_date', 'type', 'date', 'label', 'When did you make this purchase?'),
    jsonb_build_object('key', 'what_purchased', 'type', 'select', 'label', 'What did you buy together?',
      'options', jsonb_build_array('Car', 'Furniture', 'Electronics', 'Home appliances', 'Vacation', 'Other')),
    jsonb_build_object('key', 'purchase_details', 'type', 'text', 'label', 'What specifically did you buy?', 'required', true),
    jsonb_build_object('key', 'decision_process', 'type', 'textarea', 'label', 'How did you decide on this purchase?'),
    jsonb_build_object('key', 'who_initiated', 'type', 'select', 'label', 'Who brought up the idea?',
      'options', jsonb_build_array('Partner 1', 'Partner 2', 'Mutual decision')),
    jsonb_build_object('key', 'financial_arrangement', 'type', 'textarea', 'label', 'How did you handle the finances?'),
    jsonb_build_object('key', 'significance', 'type', 'textarea', 'label', 'Why was this purchase significant for your relationship?'),
    jsonb_build_object('key', 'photos', 'type', 'photo_array', 'label', 'Photos of your purchase')
  )
), jsonb_build_object('icon', '💰', 'color', '#DAA520'), 12, false),

-- Getting a Pet
('getting_pet_together', 'commitment', 'Getting a Pet Together', 'Your first shared responsibility',
jsonb_build_object(
  'fields', jsonb_build_array(
    jsonb_build_object('key', 'pet_date', 'type', 'date', 'label', 'When did you get your pet?'),
    jsonb_build_object('key', 'pet_type', 'type', 'select', 'label', 'What kind of pet?',
      'options', jsonb_build_array('Dog', 'Cat', 'Bird', 'Fish', 'Rabbit', 'Other')),
    jsonb_build_object('key', 'pet_name', 'type', 'text', 'label', 'What\'s your pet\'s name?', 'required', true),
    jsonb_build_object('key', 'whose_idea', 'type', 'select', 'label', 'Whose idea was it to get a pet?',
      'options', jsonb_build_array('Partner 1', 'Partner 2', 'Mutual decision')),
    jsonb_build_object('key', 'selection_story', 'type', 'textarea', 'label', 'How did you choose this specific pet?'),
    jsonb_build_object('key', 'first_days', 'type', 'textarea', 'label', 'How were the first few days?'),
    jsonb_build_object('key', 'care_arrangement', 'type', 'textarea', 'label', 'How do you share pet care responsibilities?'),
    jsonb_build_object('key', 'photos', 'type', 'photo_array', 'label', 'Photos of your pet')
  )
), jsonb_build_object('icon', '🐕', 'color', '#8B4513'), 13, false),

-- First "We" Instead of "I"
('first_we_moment', 'commitment', 'First "We" Moment', 'When you started thinking as a partnership',
jsonb_build_object(
  'fields', jsonb_build_array(
    jsonb_build_object('key', 'realization_date', 'type', 'date', 'label', 'When did you notice this shift?'),
    jsonb_build_object('key', 'context', 'type', 'textarea', 'label', 'What was the situation?', 'required', true),
    jsonb_build_object('key', 'who_noticed', 'type', 'select', 'label', 'Who noticed the shift first?',
      'options', jsonb_build_array('Partner 1', 'Partner 2', 'Both noticed')),
    jsonb_build_object('key', 'examples', 'type', 'textarea', 'label', 'Examples of saying "we" instead of "I"'),
    jsonb_build_object('key', 'feelings', 'type', 'textarea', 'label', 'How did this realization make you feel?'),
    jsonb_build_object('key', 'significance', 'type', 'textarea', 'label', 'Why was this moment significant?')
  )
), jsonb_build_object('icon', '👫', 'color', '#9370DB'), 14, false);

-- ============================================================================
-- ENGAGEMENT & BEYOND MILESTONES
-- ============================================================================

INSERT INTO milestone_templates (milestone_key, category, title, description, field_schema, ui_config, display_order, is_core_milestone) VALUES

-- Ring Shopping
('ring_shopping', 'engagement', 'Ring Shopping & Selection', 'The process of choosing the perfect ring',
jsonb_build_object(
  'fields', jsonb_build_array(
    jsonb_build_object('key', 'shopping_start_date', 'type', 'date', 'label', 'When did ring shopping begin?'),
    jsonb_build_object('key', 'shopping_approach', 'type', 'select', 'label', 'How did you approach ring shopping?',
      'options', jsonb_build_array('Shopped together', 'Surprise shopping', 'Hints and guidance', 'Family ring')),
    jsonb_build_object('key', 'stores_visited', 'type', 'text_array', 'label', 'Which stores did you visit?'),
    jsonb_build_object('key', 'selection_story', 'type', 'textarea', 'label', 'How did you choose the final ring?', 'required', true),
    jsonb_build_object('key', 'ring_details', 'type', 'textarea', 'label', 'Describe the ring'),
    jsonb_build_object('key', 'budget_process', 'type', 'textarea', 'label', 'How did you handle the budget discussion?'),
    jsonb_build_object('key', 'photos', 'type', 'photo_array', 'label', 'Photos of ring shopping or the ring')
  )
), jsonb_build_object('icon', '💍', 'color', '#FFD700'), 15, false),

-- Proposal Planning
('proposal_planning', 'engagement', 'Proposal Planning', 'The behind-the-scenes of the proposal',
jsonb_build_object(
  'fields', jsonb_build_array(
    jsonb_build_object('key', 'planning_duration', 'type', 'select', 'label', 'How long was the proposal planned?',
      'options', jsonb_build_array('A few days', 'A few weeks', 'A few months', 'More than 6 months')),
    jsonb_build_object('key', 'who_knew', 'type', 'text_array', 'label', 'Who knew about the proposal plan?'),
    jsonb_build_object('key', 'planning_challenges', 'type', 'textarea', 'label', 'What challenges came up during planning?'),
    jsonb_build_object('key', 'location_selection', 'type', 'textarea', 'label', 'How did you choose the proposal location?'),
    jsonb_build_object('key', 'backup_plans', 'type', 'textarea', 'label', 'Did you have any backup plans?'),
    jsonb_build_object('key', 'proposal_date', 'type', 'date', 'label', 'When did the proposal happen?', 'required', true),
    jsonb_build_object('key', 'proposal_story', 'type', 'textarea', 'label', 'Tell the proposal story', 'required', true),
    jsonb_build_object('key', 'response', 'type', 'textarea', 'label', 'How did they respond?'),
    jsonb_build_object('key', 'photos', 'type', 'photo_array', 'label', 'Proposal photos')
  )
), jsonb_build_object('icon', '💐', 'color', '#FF1493'), 16, true),

-- Engagement Party
('engagement_party', 'engagement', 'Engagement Party', 'Celebrating your engagement with loved ones',
jsonb_build_object(
  'fields', jsonb_build_array(
    jsonb_build_object('key', 'party_date', 'type', 'date', 'label', 'When was your engagement party?'),
    jsonb_build_object('key', 'location', 'type', 'text', 'label', 'Where was it held?'),
    jsonb_build_object('key', 'who_organized', 'type', 'select', 'label', 'Who organized the party?',
      'options', jsonb_build_array('We organized it', 'Family organized', 'Friends organized', 'Professional planner')),
    jsonb_build_object('key', 'guest_count', 'type', 'number', 'label', 'How many guests attended?'),
    jsonb_build_object('key', 'party_style', 'type', 'text', 'label', 'What style of party was it?'),
    jsonb_build_object('key', 'memorable_moments', 'type', 'textarea', 'label', 'Most memorable moments'),
    jsonb_build_object('key', 'speeches', 'type', 'textarea', 'label', 'Any memorable speeches or toasts?'),
    jsonb_build_object('key', 'photos', 'type', 'photo_array', 'label', 'Party photos')
  )
), jsonb_build_object('icon', '🎉', 'color', '#FF69B4'), 17, false);

-- ============================================================================
-- MODERN RELATIONSHIP MARKERS
-- ============================================================================

INSERT INTO milestone_templates (milestone_key, category, title, description, field_schema, ui_config, display_order, is_core_milestone) VALUES

-- Social Media Official
('social_media_official', 'modern', 'Social Media Official', 'When your relationship went public online',
jsonb_build_object(
  'fields', jsonb_build_array(
    jsonb_build_object('key', 'platform', 'type', 'select', 'label', 'Which platform first?',
      'options', jsonb_build_array('Facebook', 'Instagram', 'Twitter', 'TikTok', 'Other')),
    jsonb_build_object('key', 'official_date', 'type', 'date', 'label', 'When did you go social media official?'),
    jsonb_build_object('key', 'who_posted_first', 'type', 'select', 'label', 'Who made the first public post?',
      'options', jsonb_build_array('Partner 1', 'Partner 2', 'Posted together')),
    jsonb_build_object('key', 'post_type', 'type', 'select', 'label', 'What type of post was it?',
      'options', jsonb_build_array('Relationship status change', 'Photo together', 'Story/post about relationship', 'Other')),
    jsonb_build_object('key', 'post_content', 'type', 'textarea', 'label', 'What did the post say?'),
    jsonb_build_object('key', 'reactions', 'type', 'textarea', 'label', 'How did people react?'),
    jsonb_build_object('key', 'photos', 'type', 'photo_array', 'label', 'Screenshots or photos from the post')
  )
), jsonb_build_object('icon', '📱', 'color', '#1DA1F2'), 18, false),

-- First Social Media Post Together
('first_social_post', 'modern', 'First Social Media Post Together', 'Your debut as a couple online',
jsonb_build_object(
  'fields', jsonb_build_array(
    jsonb_build_object('key', 'post_date', 'type', 'date', 'label', 'When was your first post together?'),
    jsonb_build_object('key', 'platform', 'type', 'select', 'label', 'Which platform?',
      'options', jsonb_build_array('Instagram', 'Facebook', 'TikTok', 'Twitter', 'Snapchat', 'Other')),
    jsonb_build_object('key', 'post_type', 'type', 'select', 'label', 'What type of post?',
      'options', jsonb_build_array('Photo', 'Video', 'Story', 'Status update')),
    jsonb_build_object('key', 'caption', 'type', 'textarea', 'label', 'What was the caption?'),
    jsonb_build_object('key', 'occasion', 'type', 'text', 'label', 'What was the occasion?'),
    jsonb_build_object('key', 'response', 'type', 'textarea', 'label', 'How did people respond?'),
    jsonb_build_object('key', 'photos', 'type', 'photo_array', 'label', 'The original post or screenshots')
  )
), jsonb_build_object('icon', '📸', 'color', '#E4405F'), 19, false),

-- Exchanging Keys/Passwords
('exchanging_access', 'modern', 'Exchanging Keys & Digital Access', 'Modern trust milestones',
jsonb_build_object(
  'fields', jsonb_build_array(
    jsonb_build_object('key', 'exchange_date', 'type', 'date', 'label', 'When did you start sharing access?'),
    jsonb_build_object('key', 'what_shared', 'type', 'checkbox_array', 'label', 'What did you share?',
      'options', jsonb_build_array('House/apartment keys', 'Phone passwords', 'Social media passwords', 'Email passwords', 'Streaming accounts', 'Bank account info', 'Other')),
    jsonb_build_object('key', 'who_initiated', 'type', 'select', 'label', 'Who brought up sharing access first?',
      'options', jsonb_build_array('Partner 1', 'Partner 2', 'Mutual discussion')),
    jsonb_build_object('key', 'significance', 'type', 'textarea', 'label', 'Why was this significant for your relationship?'),
    jsonb_build_object('key', 'comfort_level', 'type', 'textarea', 'label', 'How did you both feel about sharing access?'),
    jsonb_build_object('key', 'boundaries', 'type', 'textarea', 'label', 'Did you set any boundaries or agreements?')
  )
), jsonb_build_object('icon', '🔑', 'color', '#4682B4'), 20, false),

-- First Streaming Service Shared
('shared_streaming', 'modern', 'First Shared Streaming Service', 'Digital cohabitation begins',
jsonb_build_object(
  'fields', jsonb_build_array(
    jsonb_build_object('key', 'service', 'type', 'select', 'label', 'Which service did you share first?',
      'options', jsonb_build_array('Netflix', 'Hulu', 'Disney+', 'Amazon Prime', 'HBO Max', 'Spotify', 'Apple Music', 'Other')),
    jsonb_build_object('key', 'sharing_date', 'type', 'date', 'label', 'When did you start sharing?'),
    jsonb_build_object('key', 'whose_account', 'type', 'select', 'label', 'Whose account was it originally?',
      'options', jsonb_build_array('Partner 1 account', 'Partner 2 account', 'Created new joint account')),
    jsonb_build_object('key', 'first_show_together', 'type', 'text', 'label', 'What was the first show/movie you watched together?'),
    jsonb_build_object('key', 'viewing_habits', 'type', 'textarea', 'label', 'How did your viewing habits change?'),
    jsonb_build_object('key', 'funny_moments', 'type', 'textarea', 'label', 'Any funny moments about sharing the account?')
  )
), jsonb_build_object('icon', '📺', 'color', '#E50914'), 21, false);

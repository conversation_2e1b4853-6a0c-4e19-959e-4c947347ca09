# Nestled Design System (React Native + Web)

A small, focused set of tokens and templates for fast, consistent UI.

## Tokens (utils/designSystem.ts)
- Radii: xs/sm/md/lg/xl/xxl/pill
- Spacing: xxs/xs/sm/md/lg/xl/xxl
- Shadows: sm/md/lg (Platform-aware)
- Borders: hairline/thin/thick
- Translucency: whiteSoft/Medium/Strong, blackSoft/Medium
- Brand: primary (#9CAF88), secondary (#CBC3E3), pink (#F3E0DA), darkerPink (#F7D0D6), text (#393939)
- Tones: 'sage' | 'lavender' | 'pink' | 'neutral' | 'warning' | 'success' | 'info'

Use tones for component variants. Gradients are not used in RN; landing page gradients map to brand solids.

## Core Templates (components/design-system/Templates.tsx)

1. DSHeaderBar
- When: Top toolbars/nav bars with left/right slots
- Props: { title?, left?, right?, tone? }
- Example:
  <DSHeaderBar title="Profile" left={<Back />} right={<Menu />} tone="sage" />

2. DSCard
- When: Solid white cards
- Props: { padded?, style }
- Example: <DSCard padded><Text>...</Text></DSCard>

3. DSStatTile
- When: Metrics with emoji/icon, large number
- Props: { emoji, number, label, tone? }

4. DSButton
- When: CTAs. Variants: primary | secondary | pill | outline
- Props: { title, onPress, variant?, tone?, disabled?, style? }

5. DSInput
- When: Form fields
- Props: { value, onChangeText, placeholder?, style? }

6. DSGlassCard
- When: Glass-style panels (landing/waitlist/hero)
- Props: { style? }

7. DSAvatar
- When: Avatar circles with optional badge
- Props: { size?, emoji?, badge? }

8. DSListItem
- When: Rows in settings/menus
- Props: { left?, title, subtitle?, right? }

9. DSBadge
- When: Tokens/tags (soft or solid)
- Props: { label, tone?, soft? }

10. DSOverlayCard
- When: Modal content containers
- Props: { style? }

11. DSSectionHeader
- When: Section headings (with optional action)
- Props: { title, action? }

12. DSPillGroup
- When: Segmented controls/filters
- Props: { options, selected, onSelect }

## Component Reference

| Component | Primary Use | Key Props | Variants |
|-----------|-------------|-----------|----------|
| DSHeaderBar | Top navigation | title, left, right, tone | sage, lavender, pink, neutral |
| DSCard | Content containers | padded, style | solid white with shadow |
| DSStatTile | Metrics display | emoji, number, label, tone | all tones for icon background |
| DSButton | Actions/CTAs | title, onPress, variant, tone | primary, secondary, pill, outline |
| DSInput | Form fields | value, onChangeText, placeholder | rounded with border |
| DSGlassCard | Hero/highlight sections | style | translucent white with border |
| DSAvatar | Profile pictures | size, emoji, badge | circular with optional badge |
| DSListItem | Menu/settings rows | left, title, subtitle, right | flexible left/right slots |
| DSBadge | Tags/labels | label, tone, soft | soft (tinted) or solid |
| DSOverlayCard | Modal content | style | elevated white card |
| DSSectionHeader | Section titles | title, action | title with optional right action |
| DSPillGroup | Toggle groups | options, selected, onSelect | rounded pill container |

## Composition Components
- ProfileHeader, StatsOverview, FavoritesSection, AchievementsSection are composites using the DS.

## Usage Rules
- Screens only compose DS + composites. Avoid StyleSheet in screens.
- If a new style is needed, add a DS variant or a new DS component.
- Import only via components/shared (DS-prefixed exports included).

## iOS Considerations
- Safe areas: ScreenLayout or SafeAreaView wrapper
- Shadows: use token shadows (Platform.select)
- Touch targets: 44pt+
- Accessibility: provide roles/labels on interactive elements

## Migration Plan
- Replace GradientButton -> DSButton where feasible
- Replace EnhancedInput -> DSInput for new screens
- Use DSCard/DSGlassCard for new sections
- Replace ad-hoc stats with DSStatTile or StatsOverview

## Complete Examples

### Landing Page Pattern
```tsx
<DSGlassCard>
  <Text>Join Waitlist</Text>
  <DSInput value={email} onChangeText={setEmail} placeholder="Email" />
  <DSButton title="Join" variant="pill" tone="sage" onPress={handleJoin} />
  <DSBadge label="Early Access" tone="warning" soft />
</DSGlassCard>
```

### Profile Screen Pattern
```tsx
<DSHeaderBar title="Profile" tone="sage" left={<BackButton />} right={<SaveButton />} />
<ProfileHeader names="Alex & Jordan" subtitle="Together for 3 years" />
<StatsOverview items={[
  { emoji: '💕', number: 3, label: 'Years Together', iconBackgroundColor: colors.darkerPink },
  { emoji: '💬', number: 124, label: 'Shared Answers', iconBackgroundColor: colors.primary },
  { emoji: '🔥', number: 15, label: 'Day Streak', iconBackgroundColor: '#FFD700' },
  { emoji: '🌟', number: 28, label: 'Date Nights', iconBackgroundColor: colors.secondary },
]} />
```

### Settings Screen Pattern
```tsx
<DSCard>
  <DSSectionHeader title="Account" />
  <DSListItem left={<DSAvatar emoji="👤" />} title="Profile" subtitle="Edit your info" right={<Arrow />} />
  <DSListItem left={<DSAvatar emoji="🔔" />} title="Notifications" right={<DSBadge label="3" tone="warning" />} />
</DSCard>
```

### Form Screen Pattern
```tsx
<DSCard>
  <DSInput value={name} onChangeText={setName} placeholder="Your name" />
  <DSPillGroup options={['Option 1', 'Option 2']} selected={selected} onSelect={setSelected} />
  <DSButton title="Save" variant="primary" tone="sage" onPress={handleSave} />
</DSCard>
```

## Cross-Platform Notes
- All shadows use Platform.select for iOS/Android compatibility
- Glass effects use translucent backgrounds (no native blur dependency)
- Touch targets meet 44pt iOS guidelines
- Colors are brand-consistent solids (no gradients in RN implementation)

## Lint Enforcement
- eslint.config.js warns on StyleSheet.create in app/ screens
- Encourages DS component usage and centralized styling


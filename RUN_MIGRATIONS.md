# 🚀 Database Migration Guide

## ✅ **Fixed Issues:**
1. **Origin Story couple_id**: Now safely handles NULL values
2. **SQL Syntax**: Fixed all apostrophe escaping issues

## 📋 **Run These 4 Files in Order:**

### **Step 1: Update Favorites Table**
```sql
-- File: supabase-favorites-migration.sql
-- Purpose: Adds new content types to favorites table
-- Safe: Won't break existing data
```

### **Step 2: Create Timeline System** 
```sql
-- File: timeline-consolidation-migration.sql  
-- Purpose: Creates timeline_events system and migrates scrapbook data
-- Safe: Handles NULL couple_id gracefully
```

### **Step 3: Create Milestone System**
```sql
-- File: milestone-system-schema.sql
-- Purpose: Creates milestone templates and couple_milestones tables
-- Safe: New tables, no existing data affected
```

### **Step 4: Load Milestone Data**
```sql
-- File: milestone-templates-data.sql
-- Purpose: Populates 21 relationship milestone templates
-- Safe: Fixed all apostrophe syntax issues
```

## 🔧 **How to Run:**

1. **Open Supabase Dashboard** → SQL Editor
2. **Copy and paste each file** in the order above
3. **Click "Run"** for each one
4. **Check for success messages** after each migration

## ✅ **Expected Results:**

After running all 4 migrations, you'll have:

- ✅ **Enhanced favorites system** supporting 5 content types
- ✅ **Scalable timeline_events table** for unlimited memories  
- ✅ **21 comprehensive milestone templates** covering all relationship stages
- ✅ **Automatic sync** between origin_story and timeline
- ✅ **Proper indexes and RLS policies** for performance and security

## 🚨 **If You Get Errors:**

### **"couple_id contains null values"**
- **Fixed!** The migration now handles this gracefully
- It will populate couple_id where possible and skip NOT NULL constraint if needed

### **"syntax error at or near"**  
- **Fixed!** All apostrophe escaping issues resolved
- The SQL should run cleanly now

### **"relation does not exist"**
- Make sure you're running the files in the correct order
- Each migration depends on the previous ones

## 🎯 **After Migration Success:**

1. **Test the favorites system** - try favoriting a meal or date night idea
2. **Check timeline events** - should see any existing scrapbook data migrated
3. **Initialize milestones** - new couples will get all 21 milestone templates
4. **Verify sync** - origin story changes should appear in timeline

## 📞 **Need Help?**

If you encounter any issues:
1. Check the Supabase logs for detailed error messages
2. Make sure you're running as a database admin
3. Verify your database has the required tables (couples, origin_story, etc.)

The migrations are now **production-ready** and safe to run! 🎉

-- ============================================================================
-- TIMELINE CONSOLIDATION MIGRATION
-- Transforms scrapbook into scalable timeline_events system
-- ============================================================================

-- Step 1: Create the new timeline tables
-- ============================================================================

-- Timeline Events Table (replaces scrapbook)
CREATE TABLE IF NOT EXISTS timeline_events (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  couple_id UUID NOT NULL,
  
  -- Event Classification
  event_type TEXT NOT NULL CHECK (event_type IN (
    'milestone', 'date_night', 'meal', 'weekly_activity', 
    'achievement', 'custom', 'anniversary', 'photo_memory'
  )),
  
  -- Event Identity
  title TEXT NOT NULL,
  description TEXT,
  event_date DATE NOT NULL,
  
  -- Rich Metadata
  metadata JSONB DEFAULT '{}',
  
  -- Source Tracking
  source_type TEXT NOT NULL CHECK (source_type IN (
    'origin_story', 'app_activity', 'user_created', 'system_generated'
  )),
  source_id TEXT,
  
  -- User Attribution
  created_by UUID,
  
  -- Visibility & Status
  is_visible BOOLEAN DEFAULT true,
  is_featured BOOLEAN DEFAULT false,
  
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Timeline Photos Table
CREATE TABLE IF NOT EXISTS timeline_photos (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  timeline_event_id UUID NOT NULL REFERENCES timeline_events(id) ON DELETE CASCADE,
  
  -- Photo Data
  photo_url TEXT NOT NULL,
  thumbnail_url TEXT,
  caption TEXT,
  
  -- Photo Metadata
  file_size INTEGER,
  mime_type TEXT,
  width INTEGER,
  height INTEGER,
  
  -- Ordering
  display_order INTEGER DEFAULT 0,
  
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  uploaded_by UUID
);

-- Step 2: Create Performance Indexes
-- ============================================================================

-- Primary timeline queries (couple + date)
CREATE INDEX idx_timeline_events_couple_date ON timeline_events(couple_id, event_date DESC);

-- Event type filtering
CREATE INDEX idx_timeline_events_type ON timeline_events(couple_id, event_type);

-- Source tracking
CREATE INDEX idx_timeline_events_source ON timeline_events(source_type, source_id);

-- Featured events
CREATE INDEX idx_timeline_events_featured ON timeline_events(couple_id, is_featured) WHERE is_featured = true;

-- Visible events only
CREATE INDEX idx_timeline_events_visible ON timeline_events(couple_id, is_visible) WHERE is_visible = true;

-- Metadata search
CREATE INDEX idx_timeline_events_metadata ON timeline_events USING GIN (metadata);

-- Photo indexes
CREATE INDEX idx_timeline_photos_event ON timeline_photos(timeline_event_id, display_order);

-- Step 3: Migrate Existing Scrapbook Data
-- ============================================================================

-- Migrate existing scrapbook entries to timeline_events
INSERT INTO timeline_events (
  couple_id,
  event_type,
  title,
  description,
  event_date,
  metadata,
  source_type,
  created_by,
  created_at,
  updated_at
)
SELECT 
  -- Map user_id to couple_id (assuming 1:1 relationship for now)
  (SELECT id FROM couples WHERE partner1_user_id = s.user_id OR partner2_user_id = s.user_id LIMIT 1) as couple_id,
  'photo_memory' as event_type,
  COALESCE(
    (s.entries->0->>'title'), 
    'Memory from ' || TO_CHAR(s.created_at, 'Month DD, YYYY')
  ) as title,
  COALESCE(
    (s.entries->0->>'content'),
    'A special memory from our journey together'
  ) as description,
  COALESCE(
    (s.entries->0->>'date')::DATE,
    s.created_at::DATE
  ) as event_date,
  jsonb_build_object(
    'migrated_from_scrapbook', true,
    'original_entries', s.entries,
    'original_photos', s.photos,
    'migration_date', NOW()
  ) as metadata,
  'user_created' as source_type,
  s.user_id as created_by,
  s.created_at,
  s.updated_at
FROM scrapbook s
WHERE s.entries IS NOT NULL 
  AND jsonb_array_length(s.entries) > 0
  AND EXISTS (
    SELECT 1 FROM couples 
    WHERE partner1_user_id = s.user_id OR partner2_user_id = s.user_id
  );

-- Step 4: Update Origin Story Table Structure
-- ============================================================================

-- Add couple_id to origin_story if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'origin_story' AND column_name = 'couple_id'
  ) THEN
    -- Add couple_id column
    ALTER TABLE origin_story ADD COLUMN couple_id UUID;
    
    -- Populate couple_id from existing user_id
    UPDATE origin_story
    SET couple_id = (
      SELECT id FROM couples
      WHERE partner1_user_id = origin_story.user_id
         OR partner2_user_id = origin_story.user_id
      LIMIT 1
    )
    WHERE couple_id IS NULL;

    -- Only make couple_id NOT NULL if all records have been populated
    -- Check if any records still have NULL couple_id
    IF NOT EXISTS (SELECT 1 FROM origin_story WHERE couple_id IS NULL) THEN
      ALTER TABLE origin_story ALTER COLUMN couple_id SET NOT NULL;
    ELSE
      RAISE NOTICE 'Some origin_story records could not be linked to couples - skipping NOT NULL constraint';
    END IF;
    
    -- Add unique constraint
    ALTER TABLE origin_story ADD CONSTRAINT origin_story_couple_id_unique UNIQUE (couple_id);
  END IF;
END $$;

-- Step 5: Create Sync Function for Origin Story → Timeline Events
-- ============================================================================

CREATE OR REPLACE FUNCTION sync_origin_story_to_timeline()
RETURNS TRIGGER AS $$
BEGIN
  -- Sync first meeting milestone
  IF NEW.first_meeting_date IS NOT NULL AND NEW.first_meeting_story IS NOT NULL THEN
    INSERT INTO timeline_events (
      couple_id, event_type, title, description, event_date, 
      metadata, source_type, source_id, created_by
    ) VALUES (
      NEW.couple_id,
      'milestone',
      'Our First Meeting',
      NEW.first_meeting_story,
      NEW.first_meeting_date,
      jsonb_build_object(
        'milestone_type', 'first_meeting',
        'imported_from_origin_story', true
      ),
      'origin_story',
      NEW.id::TEXT,
      NEW.last_updated_by
    )
    ON CONFLICT (couple_id, source_type, source_id) 
    DO UPDATE SET
      title = EXCLUDED.title,
      description = EXCLUDED.description,
      event_date = EXCLUDED.event_date,
      updated_at = NOW();
  END IF;

  -- Sync first kiss milestone
  IF NEW.first_kiss_date IS NOT NULL AND NEW.first_kiss_story IS NOT NULL THEN
    INSERT INTO timeline_events (
      couple_id, event_type, title, description, event_date,
      metadata, source_type, source_id, created_by
    ) VALUES (
      NEW.couple_id,
      'milestone', 
      'Our First Kiss',
      NEW.first_kiss_story,
      NEW.first_kiss_date,
      jsonb_build_object(
        'milestone_type', 'first_kiss',
        'imported_from_origin_story', true
      ),
      'origin_story',
      NEW.id::TEXT,
      NEW.last_updated_by
    )
    ON CONFLICT (couple_id, source_type, source_id)
    DO UPDATE SET
      title = EXCLUDED.title,
      description = EXCLUDED.description, 
      event_date = EXCLUDED.event_date,
      updated_at = NOW();
  END IF;

  -- Add more milestones as needed...
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic sync
DROP TRIGGER IF EXISTS sync_origin_story_trigger ON origin_story;
CREATE TRIGGER sync_origin_story_trigger
  AFTER INSERT OR UPDATE ON origin_story
  FOR EACH ROW
  EXECUTE FUNCTION sync_origin_story_to_timeline();

-- Step 6: Enable Row Level Security
-- ============================================================================

ALTER TABLE timeline_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE timeline_photos ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Couples can manage own timeline events" ON timeline_events
  FOR ALL USING (
    couple_id IN (
      SELECT id FROM couples 
      WHERE partner1_user_id = auth.uid() OR partner2_user_id = auth.uid()
    )
  );

CREATE POLICY "Couples can manage own timeline photos" ON timeline_photos
  FOR ALL USING (
    timeline_event_id IN (
      SELECT id FROM timeline_events 
      WHERE couple_id IN (
        SELECT id FROM couples 
        WHERE partner1_user_id = auth.uid() OR partner2_user_id = auth.uid()
      )
    )
  );

-- Step 7: Create Update Triggers
-- ============================================================================

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_timeline_events_updated_at 
  BEFORE UPDATE ON timeline_events 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Step 8: Verification Queries
-- ============================================================================

-- Show migration results
SELECT 
  'Migration Summary' as status,
  (SELECT COUNT(*) FROM timeline_events) as total_timeline_events,
  (SELECT COUNT(*) FROM timeline_events WHERE source_type = 'user_created') as migrated_from_scrapbook,
  (SELECT COUNT(*) FROM timeline_events WHERE source_type = 'origin_story') as synced_from_origin_story,
  (SELECT COUNT(DISTINCT couple_id) FROM timeline_events) as couples_with_events;

-- Show sample timeline events
SELECT
  event_type,
  title,
  event_date,
  source_type,
  created_at
FROM timeline_events
ORDER BY event_date DESC
LIMIT 10;

-- Step 9: Cleanup (Run this AFTER verifying migration success)
-- ============================================================================

-- IMPORTANT: Only run this after confirming the migration worked correctly!
--
-- -- Drop the old scrapbook table
-- DROP TABLE IF EXISTS scrapbook;
--
-- -- Remove scrapbook references from RLS policies file
-- -- Update any remaining application code references

-- Migration completed successfully!
SELECT 'Timeline consolidation migration completed!' as status;

-- ============================================================================
-- FLEXIBLE MILESTONE SYSTEM SCHEMA
-- Data-driven milestone configuration without hardcoded schema columns
-- ============================================================================

-- ============================================================================
-- 1. MILESTONE TEMPLATES TABLE (Configuration as Data)
-- ============================================================================
CREATE TABLE IF NOT EXISTS milestone_templates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  
  -- Template Identity
  milestone_key TEXT NOT NULL UNIQUE, -- e.g., 'first_meeting', 'first_date'
  category TEXT NOT NULL, -- 'foundation', 'getting_to_know', 'commitment', 'engagement', 'modern'
  title TEXT NOT NULL, -- Display name: "How We Met"
  description TEXT, -- Helper text for users
  
  -- Template Configuration
  field_schema JSONB NOT NULL, -- Defines what fields this milestone captures
  ui_config JSONB DEFAULT '{}', -- UI rendering configuration
  
  -- Ordering & Visibility
  display_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  is_core_milestone BOOLEAN DEFAULT false, -- Maps to origin_story fields
  
  -- Metadata
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- 2. COUPLE MILESTONES TABLE (Instance Data)
-- ============================================================================
CREATE TABLE IF NOT EXISTS couple_milestones (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  couple_id UUID NOT NULL,
  milestone_template_id UUID NOT NULL REFERENCES milestone_templates(id),
  
  -- Milestone Data (flexible structure based on template)
  milestone_data JSONB NOT NULL DEFAULT '{}',
  
  -- Status & Completion
  is_completed BOOLEAN DEFAULT false,
  completion_date DATE, -- When this milestone occurred
  completed_by UUID, -- Which partner filled it out
  
  -- Timeline Integration
  timeline_event_id UUID, -- Reference to generated timeline_events record
  
  -- Metadata
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(couple_id, milestone_template_id)
);

-- ============================================================================
-- 3. PERFORMANCE INDEXES
-- ============================================================================

-- Milestone templates
CREATE INDEX idx_milestone_templates_category ON milestone_templates(category, display_order);
CREATE INDEX idx_milestone_templates_active ON milestone_templates(is_active) WHERE is_active = true;
CREATE INDEX idx_milestone_templates_core ON milestone_templates(is_core_milestone) WHERE is_core_milestone = true;

-- Couple milestones
CREATE INDEX idx_couple_milestones_couple ON couple_milestones(couple_id);
CREATE INDEX idx_couple_milestones_completed ON couple_milestones(couple_id, is_completed);
CREATE INDEX idx_couple_milestones_date ON couple_milestones(couple_id, completion_date);
CREATE INDEX idx_couple_milestones_template ON couple_milestones(milestone_template_id);

-- JSONB search indexes
CREATE INDEX idx_milestone_templates_schema ON milestone_templates USING GIN (field_schema);
CREATE INDEX idx_couple_milestones_data ON couple_milestones USING GIN (milestone_data);

-- ============================================================================
-- 4. ROW LEVEL SECURITY
-- ============================================================================

ALTER TABLE milestone_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE couple_milestones ENABLE ROW LEVEL SECURITY;

-- Templates are readable by all authenticated users
CREATE POLICY "Anyone can view milestone templates" ON milestone_templates
  FOR SELECT USING (auth.role() = 'authenticated');

-- Only admins can modify templates
CREATE POLICY "Only admins can modify milestone templates" ON milestone_templates
  FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- Couples can only see their own milestones
CREATE POLICY "Couples can manage own milestones" ON couple_milestones
  FOR ALL USING (
    couple_id IN (
      SELECT id FROM couples 
      WHERE partner1_user_id = auth.uid() OR partner2_user_id = auth.uid()
    )
  );

-- ============================================================================
-- 5. AUTOMATIC TIMELINE SYNC FUNCTION
-- ============================================================================

CREATE OR REPLACE FUNCTION sync_milestone_to_timeline()
RETURNS TRIGGER AS $$
DECLARE
  template_record milestone_templates%ROWTYPE;
  timeline_title TEXT;
  timeline_description TEXT;
  timeline_metadata JSONB;
BEGIN
  -- Only process completed milestones
  IF NEW.is_completed = false OR NEW.completion_date IS NULL THEN
    RETURN NEW;
  END IF;
  
  -- Get template information
  SELECT * INTO template_record 
  FROM milestone_templates 
  WHERE id = NEW.milestone_template_id;
  
  -- Build timeline event title and description
  timeline_title := COALESCE(
    NEW.milestone_data->>'custom_title',
    template_record.title
  );
  
  timeline_description := COALESCE(
    NEW.milestone_data->>'story',
    NEW.milestone_data->>'description',
    template_record.description
  );
  
  -- Build comprehensive metadata
  timeline_metadata := jsonb_build_object(
    'milestone_key', template_record.milestone_key,
    'milestone_category', template_record.category,
    'milestone_data', NEW.milestone_data,
    'is_milestone', true,
    'template_id', template_record.id::text
  );
  
  -- Insert or update timeline event
  INSERT INTO timeline_events (
    couple_id,
    event_type,
    title,
    description,
    event_date,
    metadata,
    source_type,
    source_id,
    created_by,
    is_featured
  ) VALUES (
    NEW.couple_id,
    'milestone',
    timeline_title,
    timeline_description,
    NEW.completion_date,
    timeline_metadata,
    'milestone_system',
    NEW.id::text,
    NEW.completed_by,
    template_record.is_core_milestone -- Core milestones are featured
  )
  ON CONFLICT (couple_id, source_type, source_id)
  DO UPDATE SET
    title = EXCLUDED.title,
    description = EXCLUDED.description,
    event_date = EXCLUDED.event_date,
    metadata = EXCLUDED.metadata,
    updated_at = NOW();
  
  -- Update the milestone record with timeline event reference
  UPDATE couple_milestones 
  SET timeline_event_id = (
    SELECT id FROM timeline_events 
    WHERE couple_id = NEW.couple_id 
      AND source_type = 'milestone_system' 
      AND source_id = NEW.id::text
  )
  WHERE id = NEW.id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic timeline sync
DROP TRIGGER IF EXISTS sync_milestone_to_timeline_trigger ON couple_milestones;
CREATE TRIGGER sync_milestone_to_timeline_trigger
  AFTER INSERT OR UPDATE ON couple_milestones
  FOR EACH ROW
  EXECUTE FUNCTION sync_milestone_to_timeline();

-- ============================================================================
-- 6. UPDATE TRIGGERS
-- ============================================================================

CREATE TRIGGER update_milestone_templates_updated_at 
  BEFORE UPDATE ON milestone_templates 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_couple_milestones_updated_at 
  BEFORE UPDATE ON couple_milestones 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- 7. HELPER FUNCTIONS
-- ============================================================================

-- Function to get milestone progress for a couple
CREATE OR REPLACE FUNCTION get_milestone_progress(p_couple_id UUID)
RETURNS TABLE(
  category TEXT,
  total_milestones BIGINT,
  completed_milestones BIGINT,
  completion_percentage NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    mt.category,
    COUNT(*) as total_milestones,
    COUNT(CASE WHEN cm.is_completed THEN 1 END) as completed_milestones,
    ROUND(
      (COUNT(CASE WHEN cm.is_completed THEN 1 END) * 100.0) / COUNT(*), 
      1
    ) as completion_percentage
  FROM milestone_templates mt
  LEFT JOIN couple_milestones cm ON mt.id = cm.milestone_template_id 
    AND cm.couple_id = p_couple_id
  WHERE mt.is_active = true
  GROUP BY mt.category
  ORDER BY mt.category;
END;
$$ LANGUAGE plpgsql;

-- Function to initialize milestones for a new couple
CREATE OR REPLACE FUNCTION initialize_couple_milestones(p_couple_id UUID)
RETURNS INTEGER AS $$
DECLARE
  milestone_count INTEGER := 0;
BEGIN
  INSERT INTO couple_milestones (couple_id, milestone_template_id)
  SELECT p_couple_id, id
  FROM milestone_templates
  WHERE is_active = true
  ON CONFLICT (couple_id, milestone_template_id) DO NOTHING;
  
  GET DIAGNOSTICS milestone_count = ROW_COUNT;
  RETURN milestone_count;
END;
$$ LANGUAGE plpgsql;

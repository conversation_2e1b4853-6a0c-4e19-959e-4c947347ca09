/**
 * Color palette for the couples app with Dark Mode Support
 * Updated with new brand colors: Green Sage, Lavender Purple, Accent Pink, Darker Pink, Charcoal Gray
 *
 * Brand Color System:
 * - Primary: Green Sage (#9CAF88) - Used for main UI elements and branding
 * - Secondary: Lavender Purple (#CBC3E3) - Used for secondary elements
 * - Accent Pink: (#F3E0DA) - Used for highlights and accent elements
 * - Darker Pink: (#F7D0D6) - Used for hover states and darker accents
 * - Text: Charcoal Gray (#393939) - Used for main text content
 *
 * Theme Support:
 * - Light Mode: Original brand colors with light backgrounds
 * - Dark Mode: Adapted colors with dark backgrounds and proper contrast
 * - Auto Mode: Follows system preference
 *
 * Accessibility: All color combinations pass WCAG AA compliance standards
 */



// Theme type definition
export type ThemeMode = 'light' | 'dark' | 'auto';

// Base brand colors (theme-independent)
export const brandColors = {
  greenSage: '#9CAF88',      // Primary brand color - Green Sage
  lavenderPurple: '#CBC3E3', // Secondary brand color - Lavender Purple
  accentPink: '#F3E0DA',     // Accent Pink for highlights
  darkerPink: '#F7D0D6',     // Darker Pink for hover states
  charcoalGray: '#393939',   // Text color - Charcoal Gray
  white: '#FFFFFF',
  black: '#000000',
} as const;

// Light theme colors
export const lightTheme = {
  // Primary Brand Colors
  greenSage: brandColors.greenSage,
  lavenderPurple: brandColors.lavenderPurple,
  accentPink: brandColors.accentPink,
  darkerPink: brandColors.darkerPink,
  charcoalGray: brandColors.charcoalGray,

  // Semantic colors
  white: brandColors.white,
  black: brandColors.black,

  // Text colors
  textPrimary: brandColors.charcoalGray,
  textSecondary: '#6B7280',
  textTertiary: '#9CA3AF',
  textInverse: brandColors.white,

  // Background colors
  background: '#FAFAFA',
  backgroundPrimary: '#FAFAFA',
  backgroundSecondary: brandColors.white,
  backgroundTertiary: brandColors.accentPink,

  // Border colors
  borderLight: brandColors.accentPink,
  borderMedium: brandColors.lavenderPurple,
  borderDark: brandColors.greenSage,
  
  // Status colors
  success: brandColors.greenSage,
  warning: '#FFC857',
  error: '#FF9B91',
  info: brandColors.lavenderPurple,

  // Interactive colors
  primary: brandColors.greenSage,
  primaryLight: '#B4C49A',
  primaryDark: '#7A9470',
  secondary: brandColors.lavenderPurple,
  secondaryLight: '#D8D0E8',
  secondaryDark: '#B8ACDB',
  
  // Accent colors (updated to new brand)
  accent1: '#F3E0DA',        // Accent pink - primary accent
  accent2: '#F7D0D6',        // Darker pink - secondary accent
  accent3: '#CBC3E3',        // Lavender purple - tertiary accent
  accent4: '#9CAF88',        // Green sage - quaternary accent

  // Additional accent colors (updated)
  deepCoral: '#E5786A',      // deeper coral variant (kept for compatibility)
  tealBlue: '#2E8C87',       // deeper teal variant (kept for compatibility)
  
  // Additional utility colors (kept for consistency)
  blue: '#60A5FA',
  blueDark: '#3B82F6',
  green: '#10B981',
  greenDark: '#059669',
  orange: '#F59E0B',
  orangeDark: '#D97706',
  red: '#EF4444',
  redDark: '#DC2626',
  purple: '#8B5CF6',
  purpleDark: '#7C3AED',
  cyan: '#06B6D4',
  cyanDark: '#0891B2',
  lime: '#84CC16',
  limeDark: '#65A30D',
  orangeRed: '#F97316',
  orangeRedDark: '#EA580C',
  pink: '#EC4899',
  pinkDark: '#DB2777',
  indigo: '#6366F6',
  indigoDark: '#4F46E5',
  lightPink: '#F8BBD9',
  lightPurple: '#E879F9',
  lightPurpleDark: '#C084FC',
  
  // Text utilities
  textGray: '#374151',
  textGrayLight: '#1F2937',
  
  // Background utilities (updated to new brand)
  backgroundGray: '#F9FAFB',                             // Light gray (kept for compatibility)
  backgroundPink: '#F3E0DA',                             // Accent pink background
  backgroundOrange: '#FEF3E2',                           // Light orange (kept for compatibility)

  // Solid colors (no gradients - using original brand colors)
  solidColors: {
    primary: '#9CAF88',           // Green Sage
    secondary: '#CBC3E3',         // Lavender Purple
    warm: '#F3E0DA',              // Accent Pink
    cool: '#F7D0D6',              // Darker Pink
    sunset: '#F7D0D6',            // Darker Pink
    ocean: '#9CAF88',             // Green Sage
    header: '#9CAF88',            // Green Sage for headers
    card: '#FFFFFF',              // White for cards

    // Module-specific solid colors (no gradients)
    moduleBlue: '#60A5FA',
    moduleGreen: '#10B981',
    moduleOrange: '#F59E0B',
    moduleRed: '#EF4444',
    modulePurple: '#8B5CF6',
    moduleCyan: '#06B6D4',
    moduleLime: '#84CC16',
    moduleOrangeRed: '#F97316',
    modulePink: '#EC4899',
    moduleIndigo: '#6366F6',
    moduleLightPurple: '#E879F9',
    moduleLightPink: '#F8BBD9',

    // Badge solid colors (no gradients)
    badgeGreen: '#10B981',
    badgeBlue: '#60A5FA',
    badgeOrange: '#F59E0B',
    badgePurple: '#E879F9',
    badgeRed: '#EF4444',
    badgePink: '#F8BBD9',

    // Status solid colors (no gradients)
    success: '#9CAF88',        // Green Sage
    warning: '#F59E0B',        // Warning color
    error: '#EF4444',          // Error color
    info: '#CBC3E3',           // Lavender Purple
  },

  // Compatibility: gradients mapped to solid colors (no visual gradients)
  gradients: {
    // Core brand
    primary: ['#9CAF88', '#9CAF88'],
    secondary: ['#CBC3E3', '#CBC3E3'],
    warm: ['#F3E0DA', '#F3E0DA'],
    cool: ['#F7D0D6', '#F7D0D6'],
    sunset: ['#F7D0D6', '#F7D0D6'],
    ocean: ['#9CAF88', '#9CAF88'],
    header: ['#9CAF88', '#9CAF88'],
    card: ['#FFFFFF', '#FFFFFF'],

    // Status (solid)
    success: ['#9CAF88', '#9CAF88'],
    warning: ['#F59E0B', '#F59E0B'],
    error: ['#EF4444', '#EF4444'],
    info: ['#CBC3E3', '#CBC3E3'],

    // Module colors (solid)
    moduleBlue: ['#60A5FA', '#60A5FA'],
    moduleGreen: ['#10B981', '#10B981'],
    moduleOrange: ['#F59E0B', '#F59E0B'],
    moduleRed: ['#EF4444', '#EF4444'],
    modulePurple: ['#8B5CF6', '#8B5CF6'],
    moduleCyan: ['#06B6D4', '#06B6D4'],
    moduleLime: ['#84CC16', '#84CC16'],
    moduleOrangeRed: ['#F97316', '#F97316'],
    modulePink: ['#EC4899', '#EC4899'],
    moduleIndigo: ['#6366F6', '#6366F6'],
    moduleLightPurple: ['#E879F9', '#E879F9'],
    moduleLightPink: ['#F8BBD9', '#F8BBD9'],
  },

  // Shadow colors
  shadow: '#000000',
  shadowLight: 'rgba(0, 0, 0, 0.1)',
  shadowMedium: 'rgba(0, 0, 0, 0.2)',
  shadowDark: 'rgba(0, 0, 0, 0.3)',
  
  // Overlay colors
  overlay: 'rgba(0, 0, 0, 0.5)',
  overlayLight: 'rgba(0, 0, 0, 0.3)',
  overlayDark: 'rgba(0, 0, 0, 0.7)',
  
  // White overlay colors
  whiteOverlayLight: 'rgba(255, 255, 255, 0.1)',
  whiteOverlayMedium: 'rgba(255, 255, 255, 0.2)',
  whiteOverlayStrong: 'rgba(255, 255, 255, 0.3)',
  whiteOverlayStronger: 'rgba(255, 255, 255, 0.9)',
} as const;

// Dark theme colors
export const darkTheme = {
  // Primary Brand Colors (adapted for dark mode)
  greenSage: '#A8C094',      // Slightly lighter green sage for dark backgrounds
  lavenderPurple: '#D4CCEB', // Slightly lighter lavender for dark backgrounds
  accentPink: '#F5E6DC',     // Slightly lighter accent pink
  darkerPink: '#F9D6DC',     // Slightly lighter darker pink
  charcoalGray: '#E5E5E5',   // Light gray for text on dark backgrounds

  // Semantic colors
  white: '#1A1A1A',          // Dark background
  black: '#FFFFFF',          // Light text

  // Text colors (inverted for dark mode)
  textPrimary: '#E5E5E5',    // Light text for dark backgrounds
  textSecondary: '#B0B0B0',  // Medium gray text
  textTertiary: '#808080',   // Darker gray text
  textInverse: '#1A1A1A',    // Dark text for light elements

  // Background colors (dark variants)
  background: '#1A1A1A',           // Dark primary background
  backgroundPrimary: '#1A1A1A',    // Dark primary background
  backgroundSecondary: '#2A2A2A',  // Slightly lighter dark for cards
  backgroundTertiary: '#3A2F2A',   // Dark variant of accent pink

  // Border colors (adapted for dark mode)
  borderLight: '#3A3A3A',          // Dark border
  borderMedium: '#4A4A4A',         // Medium dark border
  borderDark: '#5A5A5A',           // Lighter dark border

  // Status colors (adapted for dark mode)
  success: '#A8C094',        // Adapted green sage
  warning: '#FFD700',        // Brighter warning for dark backgrounds
  error: '#FF6B6B',          // Brighter error for dark backgrounds
  info: '#D4CCEB',           // Adapted lavender purple

  // Interactive colors (adapted for dark mode)
  primary: '#A8C094',        // Adapted green sage
  primaryLight: '#B8D0A4',   // Lighter variant
  primaryDark: '#98B084',    // Darker variant
  secondary: '#D4CCEB',      // Adapted lavender purple
  secondaryLight: '#E0D8F0', // Lighter variant
  secondaryDark: '#C8C0E6',  // Darker variant

  // Accent colors (adapted for dark mode)
  accent1: '#F5E6DC',        // Adapted accent pink
  accent2: '#F9D6DC',        // Adapted darker pink
  accent3: '#D4CCEB',        // Adapted lavender purple
  accent4: '#A8C094',        // Adapted green sage

  // Additional accent colors (adapted)
  deepCoral: '#F08A7A',      // Brighter coral for dark backgrounds
  tealBlue: '#4EACA7',       // Brighter teal for dark backgrounds

  // Additional utility colors (adapted for dark mode)
  blue: '#70B5FF',
  blueDark: '#5B92F6',
  green: '#20C991',
  greenDark: '#15A679',
  orange: '#FFB84D',
  orangeDark: '#E5A73D',
  red: '#FF5454',
  redDark: '#E53636',
  purple: '#9B6CF6',
  purpleDark: '#8C4CED',
  cyan: '#16C6E4',
  cyanDark: '#18A1C2',
  lime: '#94D626',
  limeDark: '#75B31D',
  orangeRed: '#FF8326',
  orangeRedDark: '#FA681C',
  pink: '#FC58A9',
  pinkDark: '#EB3787',
  indigo: '#7376F6',
  indigoDark: '#5F56E5',
  lightPink: '#FFD1E9',
  lightPurple: '#F089FF',
  lightPurpleDark: '#D094FC',

  // Text utilities (adapted)
  textGray: '#C4C4C4',
  textGrayLight: '#E0E0E0',

  // Background utilities (adapted for dark mode)
  backgroundGray: '#2A2A2A',
  backgroundPink: '#3A2F2A',
  backgroundOrange: '#3A3020',

  // Solid colors (adapted for dark mode)
  solidColors: {
    primary: '#A8C094',           // Adapted green sage
    secondary: '#D4CCEB',         // Adapted lavender purple
    warm: '#F5E6DC',              // Adapted accent pink
    cool: '#F9D6DC',              // Adapted darker pink
    sunset: '#F9D6DC',            // Adapted darker pink
    ocean: '#A8C094',             // Adapted green sage
    header: '#A8C094',            // Adapted green sage for headers
    card: '#2A2A2A',              // Dark card background

    // Module-specific solid colors (adapted)
    moduleBlue: '#70B5FF',
    moduleGreen: '#20C991',
    moduleOrange: '#FFB84D',
    moduleRed: '#FF5454',
    modulePurple: '#9B6CF6',
    moduleCyan: '#16C6E4',
    moduleLime: '#94D626',
    moduleOrangeRed: '#FF8326',
    modulePink: '#FC58A9',
    moduleIndigo: '#7376F6',
    moduleLightPurple: '#F089FF',
    moduleLightPink: '#FFD1E9',

    // Badge solid colors (adapted)
    badgeGreen: '#20C991',
    badgeBlue: '#70B5FF',
    badgeOrange: '#FFB84D',
    badgePurple: '#F089FF',
    badgeRed: '#FF5454',
    badgePink: '#FFD1E9',

    // Status solid colors (adapted)
    success: '#A8C094',
    warning: '#FFD700',
    error: '#FF5454',
    info: '#D4CCEB',
  },

  // Compatibility: gradients mapped to solid colors (adapted)
  gradients: {
    // Core brand (adapted)
    primary: ['#A8C094', '#A8C094'],
    secondary: ['#D4CCEB', '#D4CCEB'],
    warm: ['#F5E6DC', '#F5E6DC'],
    cool: ['#F9D6DC', '#F9D6DC'],
    sunset: ['#F9D6DC', '#F9D6DC'],
    ocean: ['#A8C094', '#A8C094'],
    header: ['#A8C094', '#A8C094'],
    card: ['#2A2A2A', '#2A2A2A'],

    // Status (adapted)
    success: ['#A8C094', '#A8C094'],
    warning: ['#FFD700', '#FFD700'],
    error: ['#FF5454', '#FF5454'],
    info: ['#D4CCEB', '#D4CCEB'],

    // Module colors (adapted)
    moduleBlue: ['#70B5FF', '#70B5FF'],
    moduleGreen: ['#20C991', '#20C991'],
    moduleOrange: ['#FFB84D', '#FFB84D'],
    moduleRed: ['#FF5454', '#FF5454'],
    modulePurple: ['#9B6CF6', '#9B6CF6'],
    moduleCyan: ['#16C6E4', '#16C6E4'],
    moduleLime: ['#94D626', '#94D626'],
    moduleOrangeRed: ['#FF8326', '#FF8326'],
    modulePink: ['#FC58A9', '#FC58A9'],
    moduleIndigo: ['#7376F6', '#7376F6'],
    moduleLightPurple: ['#F089FF', '#F089FF'],
    moduleLightPink: ['#FFD1E9', '#FFD1E9'],
  },

  // Shadow colors (adapted for dark mode)
  shadow: '#000000',
  shadowLight: 'rgba(0, 0, 0, 0.3)',
  shadowMedium: 'rgba(0, 0, 0, 0.5)',
  shadowDark: 'rgba(0, 0, 0, 0.7)',

  // Overlay colors (adapted)
  overlay: 'rgba(0, 0, 0, 0.7)',
  overlayLight: 'rgba(0, 0, 0, 0.5)',
  overlayDark: 'rgba(0, 0, 0, 0.9)',

  // White overlay colors (adapted to dark overlays)
  whiteOverlayLight: 'rgba(255, 255, 255, 0.05)',
  whiteOverlayMedium: 'rgba(255, 255, 255, 0.1)',
  whiteOverlayStrong: 'rgba(255, 255, 255, 0.15)',
  whiteOverlayStronger: 'rgba(255, 255, 255, 0.2)',
} as const;

// Legacy colors object (defaults to light theme for backward compatibility)
export const colors = lightTheme;

export type ColorKey = keyof typeof lightTheme;

export const getColorWithOpacity = (color: string, opacity: number): string => {
  const hex = color.replace('#', '');
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};

export const getSolidColor = (colorName: keyof typeof colors.solidColors): string => {
  return colors.solidColors[colorName];
};

// Theme-aware utility functions
export const getThemeColors = (isDarkMode: boolean, systemColorScheme?: 'light' | 'dark' | null) => {
  return isDarkMode ? darkTheme : lightTheme;
};

export const getAdaptiveColors = (themeMode: ThemeMode, systemColorScheme?: 'light' | 'dark' | null) => {
  switch (themeMode) {
    case 'dark':
      return darkTheme;
    case 'light':
      return lightTheme;
    case 'auto':
      return systemColorScheme === 'dark' ? darkTheme : lightTheme;
    default:
      return lightTheme;
  }
};

// Hook for getting theme-aware colors (to be used with settings context)
// Note: This should be used in components, not in the colors file itself
export const createUseThemeColors = () => {
  return (isDarkMode: boolean) => {
    // This will be implemented in the component that uses it
    return getThemeColors(isDarkMode);
  };
};

// Utility to get theme-aware solid color
export const getThemeSolidColor = (
  colorName: keyof typeof lightTheme.solidColors,
  isDarkMode: boolean
): string => {
  const theme = isDarkMode ? darkTheme : lightTheme;
  return theme.solidColors[colorName];
};

export default colors;
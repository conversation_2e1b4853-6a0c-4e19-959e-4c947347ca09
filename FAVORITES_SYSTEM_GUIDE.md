# Unified Save/Favorite System - Setup & Migration Guide

## Overview

This guide provides comprehensive instructions for implementing and migrating to the new unified save/favorite system across your Everlasting Us app.

## 🎯 Benefits

✅ **Consistency**: All save/favorite operations work the same way  
✅ **Reliability**: Optimistic updates + error recovery + offline support  
✅ **Performance**: Intelligent caching + batch operations  
✅ **Maintainability**: Single source of truth for favorite logic  
✅ **Extensibility**: Easy to add new content types  
✅ **Developer Experience**: Simple, predictable API  

## 🚀 Quick Start

### 1. Database Setup

Run the migration script in your Supabase SQL Editor:

```sql
-- Run this first if you have existing data
\i supabase-favorites-migration.sql

-- Or run this for new installations
\i supabase-favorites-table.sql
```

### 2. App Setup

Add the FavoritesProvider to your app root:

```tsx
// App.tsx or _layout.tsx
import { FavoritesProvider } from './contexts/FavoritesContext';

export default function App() {
  return (
    <AuthProvider>
      <FavoritesProvider>
        {/* Your app content */}
      </FavoritesProvider>
    </AuthProvider>
  );
}
```

### 3. Install Dependencies

```bash
npm install @react-native-async-storage/async-storage @react-native-community/netinfo
```

## 📚 Usage Examples

### Basic Usage

```tsx
import { useMealFavorites } from '../hooks/shared/useContentFavorites';
import { HeartToggle } from '../components/shared';

const MealCard = ({ meal }) => {
  const { toggleFavorite, isFavorited, loadingStates } = useMealFavorites();

  return (
    <View>
      <HeartToggle
        isFavorited={isFavorited(meal.id)}
        onToggle={() => toggleFavorite(meal.id)}
        disabled={loadingStates[meal.id]}
      />
    </View>
  );
};
```

### Advanced Usage with Metadata

```tsx
const AdvancedMealCard = ({ meal }) => {
  const { toggleFavorite, isFavorited, updateMetadata } = useMealFavorites({
    onSuccess: (itemId, isFavorited) => {
      console.log(`Meal ${itemId} ${isFavorited ? 'saved' : 'removed'}`);
    },
    onError: (itemId, error) => {
      Alert.alert('Error', 'Failed to update favorite');
    },
  });

  const handleToggle = async () => {
    await toggleFavorite(meal.id, {
      title: meal.title,
      category: meal.category,
      savedAt: Date.now(),
    });
  };

  const addNote = async (note: string) => {
    await updateMetadata(meal.id, { note });
  };

  return (
    <View>
      <HeartToggle
        isFavorited={isFavorited(meal.id)}
        onToggle={handleToggle}
      />
    </View>
  );
};
```

## 🔄 Migration Guide

### Step 1: Identify Current Patterns

Find components using these old patterns:
- Direct `favoritesService` calls
- Manual error handling in components
- TODO comments for save/favorite functionality
- Inconsistent state management

### Step 2: Replace with Unified Hooks

| Content Type | Old Pattern | New Hook |
|--------------|-------------|----------|
| Meals | `favoritesService.toggleFavorite(userId, itemId, 'meal', ...)` | `useMealFavorites()` |
| Date Nights | `favoritesService.toggleFavorite(userId, itemId, 'date_night', ...)` | `useDateNightFavorites()` |
| Memories | TODO implementations | `useMemoryFavorites()` |
| Couple Profiles | Manual save logic | `useCoupleProfileSaves()` |
| Playlist Songs | N/A | `usePlaylistSongFavorites()` |

### Step 3: Update Components

See `examples/MigrationExamples.tsx` for detailed before/after examples.

## 🎣 Available Hooks

### Core Hook
- `useFavorites(options)` - Base hook for any content type

### Specialized Hooks
- `useMealFavorites(options?)` - For meal ideas
- `useDateNightFavorites(options?)` - For date night ideas  
- `useMemoryFavorites(options?)` - For scrapbook memories
- `useCoupleProfileSaves(options?)` - For couple profile bookmarks
- `usePlaylistSongFavorites(options?)` - For playlist songs

### Hook Options

```tsx
interface UseFavoritesOptions {
  contentType: FavoriteContentType; // Auto-set in specialized hooks
  initialFavoriteIds?: string[];    // Pre-populate favorites
  debounceMs?: number;              // API call delay (default: 300ms)
  autoLoad?: boolean;               // Auto-load user favorites (default: true)
  onFavoriteChange?: (itemId: string, isFavorited: boolean) => void;
  onSuccess?: (itemId: string, isFavorited: boolean) => void;
  onError?: (itemId: string, error: Error) => void;
}
```

### Hook Return Values

```tsx
interface UseFavoritesReturn {
  favorites: Record<string, boolean>;           // Favorite status by item ID
  loadingStates: Record<string, boolean>;       // Loading status by item ID
  isLoading: boolean;                           // Overall loading state
  error: string | null;                         // Error state
  favoriteItems: FavoriteItem[];                // Full favorite objects
  toggleFavorite: (itemId: string, metadata?: Record<string, any>) => Promise<void>;
  isFavorited: (itemId: string) => boolean;
  checkMultipleFavorites: (itemIds: string[]) => Promise<void>;
  refresh: () => Promise<void>;
  clear: () => void;
  updateMetadata: (itemId: string, metadata: Record<string, any>) => Promise<void>;
}
```

## 🌐 Offline Support

The system automatically handles offline scenarios:

- **Online**: Direct API calls with immediate feedback
- **Offline**: Operations queued for later sync
- **Reconnection**: Automatic queue processing when online
- **Retry Logic**: Exponential backoff for failed operations
- **Persistence**: Queue survives app restarts

## 🔧 Troubleshooting

### Common Issues

1. **"useFavoritesContext must be used within a FavoritesProvider"**
   - Ensure `FavoritesProvider` wraps your app

2. **Favorites not syncing across components**
   - Check that all components use the same content type
   - Verify FavoritesProvider is properly set up

3. **Database errors**
   - Run the migration script
   - Check Supabase RLS policies
   - Verify user authentication

### Debug Mode

Enable detailed logging:

```tsx
const { toggleFavorite } = useMealFavorites({
  onSuccess: (itemId, isFavorited) => {
    console.log('✅ Success:', itemId, isFavorited);
  },
  onError: (itemId, error) => {
    console.error('❌ Error:', itemId, error);
  },
});
```

## 📈 Performance Tips

1. **Use `initialFavoriteIds`** when you know items are favorited
2. **Batch check multiple items** with `checkMultipleFavorites()`
3. **Set `autoLoad: false`** if you don't need all user favorites
4. **Use metadata sparingly** - only store essential data

## 🔮 Future Enhancements

- Real-time sync across devices
- Favorite collections/folders
- Bulk operations UI
- Analytics integration
- Export/import functionality

## 📞 Support

For questions or issues:
1. Check the migration examples in `examples/MigrationExamples.tsx`
2. Review the implementation in `hooks/shared/useFavorites.ts`
3. Test with the provided database scripts

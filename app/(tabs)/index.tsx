/**
 * Home Screen - Clean Version
 * 
 * Clean, maintainable version of the home screen using shared components and custom hooks.
 * This demonstrates the final refactored approach with separated concerns.
 * 
 * <AUTHOR> Us Team
 * @version 2.0.0
 */

import React, { useState, useEffect } from 'react';
import { StyleSheet, View, ScrollView } from 'react-native';
import { useUserProfile } from '../../hooks/useUserProfile';
import { usePointsSystemSupabase } from '../../hooks/usePointsSystemSupabase';
import { useDateNightIdeasSupabase } from '../../hooks/useDateNightIdeasSupabase';
import { useEngagementSystem } from '../../hooks/useEngagementSystem';
import { colors } from '../../utils/colors';
import HamburgerMenu from '../../components/HamburgerMenu';
import { useHomeScreen } from '../../hooks/useHomeScreen';
import { secureStorage } from '../../utils/secureStorage';
import { useGlobalTheme } from '../../components/shared/ThemeProvider';

// Import shared components
import {
  ProfileHeader,
  StatsOverview,
  FavoritesSection,
  AchievementsSection,
} from '../../components/shared';

export default function HomeScreen() {
  const { getCoupleNames, getPartnerNames, profile } = useUserProfile();
  const { totalPoints, achievements, level } = usePointsSystemSupabase();
  const { userIdeas } = useDateNightIdeasSupabase();
  const { engagementStats, challengeStreak } = useEngagementSystem();

  const {
    currentWeek,
    setCurrentWeek,
    showAddGoalModal,
    newGoalText,
    setNewGoalText,
    getCurrentWeekActivities,
    getWeekProgress,
    getAllGoals,
    getGreeting,
    getNextMilestone,
    addCustomGoal,
    openAddGoalModal,
    closeAddGoalModal,
  } = useHomeScreen();

  // State for profile pictures
  const [profilePictures, setProfilePictures] = useState<{partner1?: string, partner2?: string}>({});

  // Load profile pictures on mount
  useEffect(() => {
    loadProfilePictures();
  }, []);

  const loadProfilePictures = async () => {
    try {
      const stored = await secureStorage.getItem<string>('profile_pictures');
      if (stored) {
        setProfilePictures(JSON.parse(stored));
      }
    } catch (error) {
      console.error('Error loading profile pictures:', error);
    }
  };

  // Calculate real statistics
  const partnerNames = getPartnerNames();
  const currentStreak = challengeStreak?.currentStreak || 0;
  const yearsTogetherCount = calculateYearsTogether();
  const sharedAnswersCount = calculateSharedAnswers();
  const dateNightsCount = userIdeas?.filter(idea => idea.status === 'completed').length || 0;

  // Helper functions for calculations
  function calculateYearsTogether(): number {
    if (!profile?.createdAt) return 0;
    const createdDate = new Date(profile.createdAt);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - createdDate.getTime());
    const diffYears = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 365));
    return Math.max(1, diffYears); // At least 1 year for display purposes
  }

  function calculateSharedAnswers(): number {
    // This would ideally come from aggregating all week data
    // For now, use engagement stats or a reasonable estimate based on points
    return Math.floor((totalPoints || 0) / 10); // Rough estimate: 10 points per shared answer
  }

  // Get real favorite activities from user data
  const getFavoriteActivities = () => {
    const defaultActivities = [
      { emoji: '🍕', text: 'Pizza & Movie Nights' },
      { emoji: '🥾', text: 'Weekend Hiking Adventures' },
      { emoji: '🍳', text: 'Cooking Together' },
    ];

    // If we have completed date night ideas, use those as favorites
    const completedIdeas = userIdeas?.filter(idea => idea.status === 'completed' || idea.status === 'favorite').slice(0, 3);
    if (completedIdeas && completedIdeas.length > 0) {
      // This would need to be enhanced to get the actual idea details
      return defaultActivities; // For now, return defaults
    }

    return defaultActivities;
  };

  // Get real achievements
  const getRecentAchievements = () => {
    const defaultAchievements = [
      { emoji: '🏆', name: 'Connection Master', desc: `${currentStreak}-day streak completed!` },
      { emoji: '⭐', name: 'Level ' + (level || 1), desc: `Reached level ${level || 1}!` },
    ];

    // Use real achievements if available
    if (achievements && achievements.length > 0) {
      return achievements.slice(0, 2).map(achievement => ({
        emoji: achievement.type === 'streak' ? '🔥' : achievement.type === 'level' ? '⭐' : '🏆',
        name: achievement.title,
        desc: achievement.description,
      }));
    }

    return defaultAchievements;
  };

  return (
    <View style={styles.container}>
      <HamburgerMenu position="top-right" />

      {/* Profile Header */}
      <ProfileHeader
        names={getCoupleNames()}
        subtitle={`Together for ${yearsTogetherCount} beautiful years`}
        leftImage={profilePictures.partner1}
        rightImage={profilePictures.partner2}
        leftEmoji={profile?.partner1?.icon || '👩🏻‍🦰'}
        rightEmoji={profile?.partner2?.icon || '👨🏻‍🦱'}
      />

      {/* Scrollable Content Section */}
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Stats Overview */}
        <StatsOverview
          items={[
            { emoji: '💕', number: yearsTogetherCount, label: 'Years Together', iconBackgroundColor: colors.darkerPink },
            { emoji: '💬', number: sharedAnswersCount, label: 'Shared Answers', iconBackgroundColor: colors.primary },
            { emoji: '🔥', number: currentStreak, label: 'Day Streak', iconBackgroundColor: '#FFD700' },
            { emoji: '🌟', number: dateNightsCount, label: 'Date Nights', iconBackgroundColor: colors.secondary },
          ]}
        />

        {/* Favorites Section */}
        <FavoritesSection
          items={getFavoriteActivities()}
        />

        {/* Achievements Section */}
        <AchievementsSection
          items={getRecentAchievements()}
        />
      </ScrollView>
    </View>
  );
}



const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },



  // Content styles
  content: {
    backgroundColor: colors.accentPink,
    flex: 1,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    marginTop: 0,
  },
  scrollContent: {
    padding: 30,
    paddingBottom: 50, // Extra padding at bottom for better scrolling
  },




});
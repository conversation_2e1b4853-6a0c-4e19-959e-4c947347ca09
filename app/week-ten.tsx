import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert } from 'react-native';

import { ArrowLeft, Save, CheckCircle, Heart, Users, MessageCircle, BookOpen, Calendar, MapPin, DollarSign, Palette, TrendingUp } from 'lucide-react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { useWeekTenData } from '../hooks/useWeekTenData';
import { colors } from '../utils/colors';

export default function WeekTenScreen() {
  const params = useLocalSearchParams();
  const [currentStep, setCurrentStep] = useState(0);
  
  // Handle URL parameters for direct section navigation
  useEffect(() => {
    if (params.section) {
      const sectionIndex = parseInt(params.section as string);
      if (sectionIndex >= 0 && sectionIndex < steps.length) {
        setCurrentStep(sectionIndex);
      }
    }
  }, [params.section]);
  
  const {
    data,
    updateDreamBankGoal,
    updateGetArtsyDate,
    updateChatPrompt,
    updateMoneyTalk,
    updateCompletedSections,
  } = useWeekTenData();

  const steps = [
    { title: 'Dream Bank ($5M)', icon: <DollarSign size={24} color={colors.white} /> },
    { title: 'Get Artsy Date', icon: <Calendar size={24} color={colors.white} /> },
    { title: 'Chat Prompts', icon: <MessageCircle size={24} color={colors.white} /> },
    { title: 'Money Talk', icon: <BookOpen size={24} color={colors.white} /> },
  ];

  const handleSaveAndContinue = () => {
    if (currentStep < steps.length - 1) {
      const newSections = [...data.completedSections];
      newSections[currentStep] = true;
      updateCompletedSections(newSections);
      setCurrentStep(currentStep + 1);
    } else {
      // All steps completed
      const newSections = [...data.completedSections];
      newSections[currentStep] = true;
      updateCompletedSections(newSections);
      Alert.alert('Congratulations!', 'You\'ve completed Week Ten! All your responses have been saved to your scrapbook.');
    }
  };

  const handleComeBackLater = () => {
    router.back();
  };

  const handleAmountChange = (index: number, amount: string) => {
    const numAmount = parseFloat(amount) || 0;
    updateDreamBankGoal(index, { amount: numAmount });
  };

  const calculateTotal = () => {
    return data.dreamBankGoals.reduce((total, goal) => total + goal.amount, 0);
  };

  const renderDreamBank = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Dream Bank ($5M)</Text>
        <Text style={styles.sectionSubtitle}>Allocate money across 10 shared goals</Text>
      </View>

      <View style={styles.dreamBankCard}>
        <Text style={styles.dreamBankTitle}>Your $5 Million Dream Allocation</Text>
        
        <View style={styles.totalDisplay}>
          <Text style={styles.totalLabel}>Total Allocated:</Text>
          <Text style={styles.totalAmount}>${calculateTotal().toLocaleString()}</Text>
          <Text style={styles.totalRemaining}>
            Remaining: ${(5000000 - calculateTotal()).toLocaleString()}
          </Text>
        </View>

        {data.dreamBankGoals?.map((goal, index) => (
          <View key={index} style={styles.goalRow}>
            <View style={styles.goalInfo}>
              <Text style={styles.goalName}>{goal.item}</Text>
              <Text style={styles.goalPriority}>Priority {goal.priority}</Text>
            </View>
            <View style={styles.amountInput}>
              <Text style={styles.currencySymbol}>$</Text>
              <TextInput
                style={styles.amountTextInput}
                placeholder="0"
                value={goal.amount > 0 ? goal.amount.toString() : ''}
                onChangeText={(text) => handleAmountChange(index, text)}
                keyboardType="numeric"
              />
            </View>
          </View>
        ))}

        <View style={styles.pieChartPlaceholder}>
          <TrendingUp size={48} color={colors.orangeRed} />
          <Text style={styles.pieChartText}>Visual breakdown would show here</Text>
          <Text style={styles.pieChartSubtext}>Pie chart of your dream allocation</Text>
        </View>
      </View>
    </View>
  );

  const renderGetArtsyDate = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Get Artsy Date</Text>
        <Text style={styles.sectionSubtitle}>Visit gallery/museum → discuss favorite piece</Text>
      </View>

      <View style={styles.datePlanCard}>
        <Text style={styles.datePlanTitle}>Plan Your Artsy Adventure</Text>
        
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>When:</Text>
          <TextInput
            style={styles.textInput}
            placeholder="e.g., This Saturday afternoon"
            value={data.getArtsyDate.when}
            onChangeText={(text) => updateGetArtsyDate({ when: text })}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Where:</Text>
          <TextInput
            style={styles.textInput}
            placeholder="e.g., Local art gallery, Museum of Modern Art"
            value={data.getArtsyDate.where}
            onChangeText={(text) => updateGetArtsyDate({ where: text })}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Favorite Piece Discussion:</Text>
          <TextInput
            style={[styles.textInput, { minHeight: 80 }]}
            placeholder="What piece caught your attention? Why did you choose it? What emotions did it evoke?"
            value={data.getArtsyDate.favoritePiece}
            onChangeText={(text) => updateGetArtsyDate({ favoritePiece: text })}
            multiline
          />
        </View>
      </View>
    </View>
  );

  const renderChatPrompts = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Chat Prompts</Text>
        <Text style={styles.sectionSubtitle}>Deep conversations to strengthen your bond</Text>
      </View>

      {data.chatPrompts?.map((prompt, index) => (
        <View key={index} style={styles.chatPromptCard}>
          <Text style={styles.promptText}>{prompt.prompt}</Text>
          
          <View style={styles.answerInputs}>
            <View style={styles.answerInput}>
              <Text style={styles.playerLabel}>Player One:</Text>
              <TextInput
                style={styles.textInput}
                placeholder="Your answer..."
                value={prompt.playerOneAnswer}
                onChangeText={(text) => updateChatPrompt(index, { playerOneAnswer: text })}
                multiline
              />
            </View>
            
            <View style={styles.answerInput}>
              <Text style={styles.playerLabel}>Player Two:</Text>
              <TextInput
                style={styles.textInput}
                placeholder="Your answer..."
                value={prompt.playerTwoAnswer}
                onChangeText={(text) => updateChatPrompt(index, { playerTwoAnswer: text })}
                multiline
              />
            </View>
          </View>
        </View>
      ))}
    </View>
  );

  const renderMoneyTalk = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Money Talk</Text>
        <Text style={styles.sectionSubtitle}>Practice Ramit Sethi\'s money conversation techniques</Text>
      </View>

      <View style={styles.toolkitInstructions}>
        <Text style={styles.instructionText}>
          Questions: money history, spending habits, guilt-free joys
        </Text>
        <Text style={styles.instructionText}>
          Fields for answers + shared financial goals
        </Text>
      </View>

      <View style={styles.moneyTalkCard}>
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Money History:</Text>
          <TextInput
            style={[styles.textInput, { minHeight: 80 }]}
            placeholder="What did you learn about money growing up? What were your family's money values?"
            value={data.moneyTalk.moneyHistory}
            onChangeText={(text) => updateMoneyTalk({ moneyHistory: text })}
            multiline
          />
        </View>
        
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Spending Habits:</Text>
          <TextInput
            style={[styles.textInput, { minHeight: 80 }]}
            placeholder="What are your current spending patterns? What do you spend money on without thinking?"
            value={data.moneyTalk.spendingHabits}
            onChangeText={(text) => updateMoneyTalk({ spendingHabits: text })}
            multiline
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Guilt-Free Joys:</Text>
          <TextInput
            style={[styles.textInput, { minHeight: 80 }]}
            placeholder="What purchases or experiences bring you genuine joy without guilt? What would you spend money on if you had unlimited funds?"
            value={data.moneyTalk.guiltFreeJoys}
            onChangeText={(text) => updateMoneyTalk({ guiltFreeJoys: text })}
            multiline
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Shared Financial Goals:</Text>
          <TextInput
            style={styles.textInput}
            placeholder="What are your shared financial goals as a couple? How do you want to handle money together?"
            value={data.moneyTalk.sharedFinancialGoals}
            onChangeText={(text) => updateMoneyTalk({ sharedFinancialGoals: text })}
            multiline
          />
        </View>
      </View>
    </View>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return renderDreamBank();
      case 1:
        return renderGetArtsyDate();
      case 2:
        return renderChatPrompts();
      case 3:
        return renderMoneyTalk();
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View
        style={[styles.header, { backgroundColor: colors.orangeRed }]}
      >
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Week 10: The Dream Bank</Text>
        <Text style={styles.headerSubtitle}>Plan your financial dreams together</Text>
      </View>

      {/* Progress Steps */}
      <View style={styles.progressContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {steps.map((step, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.progressStep,
                currentStep === index && styles.progressStepActive,
                data.completedSections[index] && styles.progressStepCompleted,
              ]}
              onPress={() => setCurrentStep(index)}
            >
              {data.completedSections[index] ? (
                <CheckCircle size={16} color={colors.white} />
              ) : (
                step.icon
              )}
              <Text style={[
                styles.progressStepText,
                currentStep === index && styles.progressStepTextActive,
                data.completedSections[index] && styles.progressStepTextCompleted,
              ]}>
                {step.title}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderCurrentStep()}
      </ScrollView>

      {/* Bottom Actions */}
      <View style={styles.bottomActions}>
        <TouchableOpacity style={styles.comeBackButton} onPress={handleComeBackLater}>
          <Text style={styles.comeBackButtonText}>Come Back Later</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.continueButton} onPress={handleSaveAndContinue}>
          <View
            style={[styles.continueButtonGradient, { backgroundColor: colors.orangeRed }]}
          >
            <Text style={styles.continueButtonText}>
              {currentStep < steps.length - 1 ? 'Save & Continue' : 'Complete Week'}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundOrange,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 24,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: 20,
    zIndex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.white,
    marginBottom: 4,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 16,
    color: colors.white,
    opacity: 0.9,
    textAlign: 'center',
  },
  progressContainer: {
    backgroundColor: colors.white,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  progressStep: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 4,
    borderRadius: 20,
    backgroundColor: colors.backgroundTertiary,
  },
  progressStepActive: {
    backgroundColor: colors.orangeRed,
  },
  progressStepCompleted: {
    backgroundColor: colors.success,
  },
  progressStepText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.textSecondary,
    marginLeft: 6,
  },
  progressStepTextActive: {
    color: colors.white,
  },
  progressStepTextCompleted: {
    color: colors.white,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  sectionContainer: {
    marginTop: 24,
  },
  sectionHeader: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    lineHeight: 24,
  },
  dreamBankCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  dreamBankTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 20,
    textAlign: 'center',
  },
  totalDisplay: {
    backgroundColor: colors.backgroundOrange,
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    alignItems: 'center',
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.orangeDark,
    marginBottom: 8,
  },
  totalAmount: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.orangeDark,
    marginBottom: 4,
  },
  totalRemaining: {
    fontSize: 14,
    color: colors.orangeDark,
    opacity: 0.8,
  },
  goalRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  goalInfo: {
    flex: 1,
  },
  goalName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 4,
  },
  goalPriority: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  amountInput: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.backgroundGray,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    minWidth: 100,
  },
  currencySymbol: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginRight: 4,
  },
  amountTextInput: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    textAlign: 'right',
    flex: 1,
  },
  pieChartPlaceholder: {
    backgroundColor: colors.backgroundOrange,
    padding: 24,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 20,
  },
  pieChartText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.orangeDark,
    marginTop: 12,
    marginBottom: 4,
  },
  pieChartSubtext: {
    fontSize: 14,
    color: colors.orangeDark,
    opacity: 0.8,
    textAlign: 'center',
  },
  datePlanCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  datePlanTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 20,
    textAlign: 'center',
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: colors.borderMedium,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  chatPromptCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  promptText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 16,
    textAlign: 'center',
  },
  answerInputs: {
    gap: 16,
  },
  answerInput: {
    flex: 1,
  },
  playerLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  toolkitInstructions: {
    backgroundColor: colors.backgroundOrange,
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  instructionText: {
    fontSize: 14,
    color: colors.orangeDark,
    marginBottom: 4,
    textAlign: 'center',
  },
  moneyTalkCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  bottomActions: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
    gap: 12,
  },
  comeBackButton: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.borderMedium,
    alignItems: 'center',
  },
  comeBackButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textSecondary,
  },
  continueButton: {
    flex: 2,
    borderRadius: 12,
    overflow: 'hidden',
  },
  continueButtonGradient: {
    paddingVertical: 14,
    alignItems: 'center',
  },
  continueButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
});

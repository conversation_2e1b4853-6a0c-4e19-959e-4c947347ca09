import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { <PERSON>rror<PERSON>and<PERSON> } from '../components/ErrorHandler';
import { AuthProvider } from '../contexts/AuthContext';
import { SettingsProvider } from '../contexts/SettingsContext';
import { GlobalThemeProvider } from '../components/shared/ThemeProvider';
import { FavoritesProvider } from '../contexts/FavoritesContext';

export default function RootLayout() {
  return (
    <ErrorHandler>
      <SettingsProvider>
        <GlobalThemeProvider>
          <AuthProvider>
            <FavoritesProvider>
              <Stack screenOptions={{ headerShown: false }}>
              <Stack.Screen name="index" />
              <Stack.Screen name="onboarding" />
              <Stack.Screen name="auth" />
              <Stack.Screen name="our-story" />
              <Stack.Screen name="couple-profile" />
              <Stack.Screen name="app-settings" />
              <Stack.Screen name="notifications" />
              <Stack.Screen name="scrapbook" />
              <Stack.Screen name="(tabs)" />
              <Stack.Screen name="+not-found" />
              </Stack>
              <StatusBar style="auto" />
            </FavoritesProvider>
          </AuthProvider>
        </GlobalThemeProvider>
      </SettingsProvider>
    </ErrorHandler>
  );
}

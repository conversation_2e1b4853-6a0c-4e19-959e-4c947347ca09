import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Switch, Alert } from 'react-native';

import { ArrowLeft, Settings, Bell, Palette, Globe, Shield, Volume2, VolumeX, Vibrate, Moon, Sun, Smartphone } from 'lucide-react-native';
import { router } from 'expo-router';
import { useSettings } from '../contexts/SettingsContext';
import HamburgerMenu from '../components/HamburgerMenu';
import { DSHeaderBar, DSButton } from '../components/shared';
import { colors } from '../utils/colors';
import { useGlobalTheme } from '../components/shared/ThemeProvider';

export default function AppSettingsScreen() {
  const { settings, isLoading, updateSetting, resetSettings: contextResetSettings, currentTheme, isDarkMode, systemColorScheme } = useSettings();
  const { currentTheme: themeColors } = useGlobalTheme();

  const toggleSetting = async (key: keyof typeof settings) => {
    try {
      await updateSetting(key, !settings[key] as any);
    } catch (error) {
      Alert.alert('Error', 'Failed to save settings. Please try again.');
    }
  };

  const changeLanguage = () => {
    Alert.alert(
      'Language',
      'Select your preferred language:',
      [
        { text: 'English', onPress: () => updateSetting('language', 'English') },
        { text: 'Español', onPress: () => updateSetting('language', 'Español') },
        { text: 'Français', onPress: () => updateSetting('language', 'Français') },
        { text: 'Deutsch', onPress: () => updateSetting('language', 'Deutsch') },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const changeFontSize = () => {
    Alert.alert(
      'Font Size',
      'Select your preferred font size:',
      [
        { text: 'Small', onPress: () => updateSetting('fontSize', 'small') },
        { text: 'Medium', onPress: () => updateSetting('fontSize', 'medium') },
        { text: 'Large', onPress: () => updateSetting('fontSize', 'large') },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const changeTheme = () => {
    Alert.alert(
      'Theme Settings',
      'Choose your preferred theme',
      [
        {
          text: 'Light',
          onPress: () => updateSetting('theme', 'light'),
        },
        {
          text: 'Dark',
          onPress: () => updateSetting('theme', 'dark'),
        },
        {
          text: 'Auto (System)',
          onPress: () => updateSetting('theme', 'auto'),
        },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const getThemeDisplayText = () => {
    switch (settings.theme) {
      case 'light': return 'Light';
      case 'dark': return 'Dark';
      case 'auto': return `Auto (${systemColorScheme === 'dark' ? 'Dark' : 'Light'})`;
      default: return 'Light';
    }
  };

  const getThemeIcon = () => {
    if (settings.theme === 'auto') {
      return <Smartphone size={20} color={themeColors.primary} />;
    }
    return isDarkMode ?
      <Moon size={20} color={themeColors.primary} /> :
      <Sun size={20} color={themeColors.primary} />;
  };

  const resetSettings = () => {
    Alert.alert(
      'Reset Settings',
      'Are you sure you want to reset all app settings to default?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: async () => {
            try {
              await contextResetSettings();
              Alert.alert('Success', 'Settings reset to default values.');
            } catch (error) {
              Alert.alert('Error', 'Failed to reset settings. Please try again.');
            }
          }
        },
      ]
    );
  };

  const renderSettingItem = (
    icon: React.ReactNode,
    title: string,
    subtitle: string,
    value?: boolean | string,
    onPress?: () => void,
    isToggle = false
  ) => (
    <View style={styles.settingItem}>
      <View style={styles.settingLeft}>
        <View style={styles.settingIcon}>
          {icon}
        </View>
        <View style={styles.settingText}>
          <Text style={styles.settingTitle}>{title}</Text>
          <Text style={styles.settingSubtitle}>{subtitle}</Text>
        </View>
      </View>
      
      {isToggle ? (
        <Switch
          value={value as boolean}
          onValueChange={onPress}
          trackColor={{ false: colors.borderLight, true: colors.lightPink }}
          thumbColor={value ? colors.lightPurple : colors.textTertiary}
        />
      ) : (
        <TouchableOpacity style={styles.settingAction} onPress={onPress}>
          <Text style={styles.settingValue}>{value}</Text>
          <Text style={styles.settingArrow}>›</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading settings...</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: themeColors.background }]}>
      {/* Hamburger Menu */}
      <HamburgerMenu position="top-right" />

      {/* Header */}
      <DSHeaderBar
        title="App Settings"
        tone="pink"
        left={
          <TouchableOpacity onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.white} />
          </TouchableOpacity>
        }
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>

        {/* Theme Selection */}
        <View style={[styles.section, { backgroundColor: themeColors.backgroundSecondary }]}>
          <Text style={[styles.sectionTitle, { color: themeColors.textPrimary }]}>Theme</Text>
          <Text style={[styles.sectionDescription, { color: themeColors.textSecondary }]}>
            Choose your preferred theme
          </Text>

          {/* Theme Radio Buttons */}
          <View style={styles.radioGroup}>
            {(['light', 'dark', 'auto'] as const).map((theme) => (
              <TouchableOpacity
                key={theme}
                style={[
                  styles.radioOption,
                  { borderColor: themeColors.borderLight }
                ]}
                onPress={() => updateSetting('theme', theme)}
              >
                <View style={[
                  styles.radioCircle,
                  { borderColor: themeColors.primary },
                  settings.theme === theme && { backgroundColor: themeColors.primary }
                ]}>
                  {settings.theme === theme && (
                    <View style={[styles.radioInner, { backgroundColor: themeColors.background }]} />
                  )}
                </View>
                <View style={styles.radioContent}>
                  {getThemeIcon()}
                  <Text style={[styles.radioLabel, { color: themeColors.textPrimary }]}>
                    {theme.charAt(0).toUpperCase() + theme.slice(1)}
                    {theme === 'auto' && ` (${systemColorScheme === 'dark' ? 'Dark' : 'Light'})`}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Other Settings */}
        <View style={[styles.section, { backgroundColor: themeColors.backgroundSecondary }]}>
          <Text style={[styles.sectionTitle, { color: themeColors.textPrimary }]}>Other Settings</Text>
          {renderSettingItem(
            <Globe size={20} color={colors.lightPurple} />,
            'Language',
            'Choose your preferred language',
            settings.language,
            changeLanguage
          )}
        </View>

        {/* Data & Sync */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Data & Sync</Text>
          {renderSettingItem(
            <Shield size={20} color={colors.blue} />,
            'Auto Save',
            'Automatically save your progress',
            settings.autoSave,
            () => toggleSetting('autoSave'),
            true
          )}
          {renderSettingItem(
            <Settings size={20} color={colors.success} />,
            'Data Sync',
            'Sync data across devices',
            settings.dataSync,
            () => toggleSetting('dataSync'),
            true
          )}
        </View>

        {/* Notifications */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notifications</Text>
          {renderSettingItem(
            <Bell size={20} color={colors.primary} />,
            'Push Notifications',
            'Receive notifications for activities and reminders',
            settings.notifications,
            () => toggleSetting('notifications'),
            true
          )}
          {renderSettingItem(
            settings.soundEnabled ? <Volume2 size={20} color={colors.success} /> : <VolumeX size={20} color={colors.error} />,
            'Sound',
            'Play sounds for notifications and interactions',
            settings.soundEnabled,
            () => toggleSetting('soundEnabled'),
            true
          )}
          {renderSettingItem(
            <Vibrate size={20} color={colors.secondary} />,
            'Vibration',
            'Vibrate for notifications and feedback',
            settings.vibrationEnabled,
            () => toggleSetting('vibrationEnabled'),
            true
          )}
        </View>

        {/* Accessibility */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Accessibility</Text>
          {renderSettingItem(
            <Shield size={20} color={colors.warning} />,
            'Accessibility Features',
            'Enable enhanced accessibility options',
            settings.accessibility,
            () => toggleSetting('accessibility'),
            true
          )}
          {renderSettingItem(
            <Palette size={20} color={colors.info} />,
            'Font Size',
            `Current: ${settings.fontSize}`,
            settings.fontSize,
            () => changeFontSize(),
            false
          )}
        </View>

        {/* Actions */}
        <View style={styles.section}>
          <DSButton
            title="Reset to Default"
            variant="outline"
            tone="warning"
            onPress={resetSettings}
          />
        </View>

        {/* Info */}
        <View style={styles.infoCard}>
          <Settings size={24} color="#9CA3AF" />
          <Text style={styles.infoTitle}>Settings Saved</Text>
          <Text style={styles.infoText}>
            Your app settings are automatically saved and will persist across app restarts.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FEF3E2',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    paddingTop: 60,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  headerSpacer: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1F2937',
    marginBottom: 4,
  },
  settingSubtitle: {
    fontSize: 14,
    color: '#6B7280',
  },
  settingAction: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingValue: {
    fontSize: 16,
    color: '#6B7280',
    marginRight: 8,
  },
  settingArrow: {
    fontSize: 18,
    color: '#9CA3AF',
  },
  resetButton: {
    backgroundColor: '#FEF2F2',
    borderWidth: 1,
    borderColor: '#FECACA',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  resetButtonText: {
    color: '#DC2626',
    fontSize: 16,
    fontWeight: '600',
  },
  infoCard: {
    backgroundColor: '#F9FAFB',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    marginBottom: 20,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginTop: 12,
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
  },
  sectionDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 16,
  },
  radioGroup: {
    gap: 12,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderWidth: 1,
    borderRadius: 8,
    backgroundColor: 'transparent',
  },
  radioCircle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioInner: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  radioContent: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 12,
    gap: 8,
  },
  radioLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
});

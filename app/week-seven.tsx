import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert } from 'react-native';

import { ArrowLeft, Save, CheckCircle, Heart, Users, MessageCircle, BookOpen, Calendar, MapPin, Zap, Shield, Target } from 'lucide-react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { useWeekSevenData } from '../hooks/useWeekSevenData';
import { colors } from '../utils/colors';

export default function WeekSevenScreen() {
  const params = useLocalSearchParams();
  const [currentStep, setCurrentStep] = useState(0);
  
  // Handle URL parameters for direct section navigation
  useEffect(() => {
    if (params.section) {
      const sectionIndex = parseInt(params.section as string);
      if (sectionIndex >= 0 && sectionIndex < steps.length) {
        setCurrentStep(sectionIndex);
      }
    }
  }, [params.section]);
  
  const {
    data,
    updateSuperheroDuoChart,
    updateThriftShopShowdown,
    updateChatPrompt,
    updateTurningTowardPractice,
    updateCompletedSections,
  } = useWeekSevenData();

  const steps = [
    { title: 'Superhero Duo Chart', icon: <Zap size={24} color={colors.white} /> },
    { title: 'Thrift Shop Showdown', icon: <Calendar size={24} color={colors.white} /> },
    { title: 'Chat Prompts', icon: <MessageCircle size={24} color={colors.white} /> },
    { title: 'Turning Toward', icon: <BookOpen size={24} color={colors.white} /> },
  ];

  const handleSaveAndContinue = () => {
    if (currentStep < steps.length - 1) {
      const newSections = [...data.completedSections];
      newSections[currentStep] = true;
      updateCompletedSections(newSections);
      setCurrentStep(currentStep + 1);
    } else {
      // All steps completed
      const newSections = [...data.completedSections];
      newSections[currentStep] = true;
      updateCompletedSections(newSections);
      Alert.alert('Congratulations!', 'You\'ve completed Week Seven! All your responses have been saved to your scrapbook.');
    }
  };

  const handleComeBackLater = () => {
    router.back();
  };

  const renderSuperheroDuoChart = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Superhero Duo Chart</Text>
        <Text style={styles.sectionSubtitle}>Create your superhero identities and discover your combined powers</Text>
      </View>

      <View style={styles.superheroCard}>
        <Text style={styles.superheroTitle}>Your Superhero Identities</Text>
        
        <View style={styles.playerSection}>
          <Text style={styles.playerTitle}>Player One</Text>
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Superpower:</Text>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., Super empathy, Time manipulation"
              value={data.superheroDuoChart.playerOneSuperpower}
              onChangeText={(text) => updateSuperheroDuoChart({ playerOneSuperpower: text })}
            />
          </View>
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Weakness:</Text>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., Overthinking, Chocolate"
              value={data.superheroDuoChart.playerOneWeakness}
              onChangeText={(text) => updateSuperheroDuoChart({ playerOneWeakness: text })}
            />
          </View>
        </View>

        <View style={styles.playerSection}>
          <Text style={styles.playerTitle}>Player Two</Text>
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Superpower:</Text>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., Infinite patience, Perfect memory"
              value={data.superheroDuoChart.playerTwoSuperpower}
              onChangeText={(text) => updateSuperheroDuoChart({ playerTwoSuperpower: text })}
            />
          </View>
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Weakness:</Text>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., Bad puns, Spicy food"
              value={data.superheroDuoChart.playerTwoWeakness}
              onChangeText={(text) => updateSuperheroDuoChart({ playerTwoWeakness: text })}
            />
          </View>
        </View>

        <View style={styles.combinedSection}>
          <Text style={styles.combinedTitle}>Your Combined Powers</Text>
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Combined Superpower:</Text>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., Unstoppable love force, Perfect communication"
              value={data.superheroDuoChart.combinedPower}
              onChangeText={(text) => updateSuperheroDuoChart({ combinedPower: text })}
            />
          </View>
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Your Villain:</Text>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., Miscommunication, Busy schedules"
              value={data.superheroDuoChart.villain}
              onChangeText={(text) => updateSuperheroDuoChart({ villain: text })}
            />
          </View>
        </View>
      </View>
    </View>
  );

  const renderThriftShopShowdown = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Thrift Shop Showdown</Text>
        <Text style={styles.sectionSubtitle}>Budget + time limit → funniest/thoughtful/random gift</Text>
      </View>

      <View style={styles.datePlanCard}>
        <Text style={styles.datePlanTitle}>Plan Your Thrift Shop Adventure</Text>
        
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>When:</Text>
          <TextInput
            style={styles.textInput}
            placeholder="e.g., This Saturday afternoon"
            value={data.thriftShopShowdown.when}
            onChangeText={(text) => updateThriftShopShowdown({ when: text })}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Where:</Text>
          <TextInput
            style={styles.textInput}
            placeholder="e.g., Goodwill, Salvation Army"
            value={data.thriftShopShowdown.where}
            onChangeText={(text) => updateThriftShopShowdown({ where: text })}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Budget:</Text>
          <TextInput
            style={styles.textInput}
            placeholder="e.g., $20 total"
            value={data.thriftShopShowdown.budget}
            onChangeText={(text) => updateThriftShopShowdown({ budget: text })}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Time Limit:</Text>
          <TextInput
            style={styles.textInput}
            placeholder="e.g., 30 minutes"
            value={data.thriftShopShowdown.timeLimit}
            onChangeText={(text) => updateThriftShopShowdown({ timeLimit: text })}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Gift Description:</Text>
          <TextInput
            style={[styles.textInput, { minHeight: 80 }]}
            placeholder="Describe the gift you found and why you chose it..."
            value={data.thriftShopShowdown.giftDescription}
            onChangeText={(text) => updateThriftShopShowdown({ giftDescription: text })}
            multiline
          />
        </View>
      </View>
    </View>
  );

  const renderChatPrompts = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Chat Prompts</Text>
        <Text style={styles.sectionSubtitle}>Deep conversations to strengthen your bond</Text>
      </View>

      {data.chatPrompts.map((prompt, index) => (
        <View key={index} style={styles.chatPromptCard}>
          <Text style={styles.promptText}>{prompt.prompt}</Text>
          
          <View style={styles.answerInputs}>
            <View style={styles.answerInput}>
              <Text style={styles.playerLabel}>Player One:</Text>
              <TextInput
                style={styles.textInput}
                placeholder="Your answer..."
                value={prompt.playerOneAnswer}
                onChangeText={(text) => updateChatPrompt(index, { playerOneAnswer: text })}
                multiline
              />
            </View>
            
            <View style={styles.answerInput}>
              <Text style={styles.playerLabel}>Player Two:</Text>
              <TextInput
                style={styles.textInput}
                placeholder="Your answer..."
                value={prompt.playerTwoAnswer}
                onChangeText={(text) => updateChatPrompt(index, { playerTwoAnswer: text })}
                multiline
              />
            </View>
          </View>
        </View>
      ))}
    </View>
  );

  const renderTurningToward = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Turning Toward</Text>
        <Text style={styles.sectionSubtitle}>Practice Dr. John Gottman\'s technique</Text>
      </View>

      <View style={styles.toolkitInstructions}>
        <Text style={styles.instructionText}>
          Recognize & respond to bids for connection
        </Text>
        <Text style={styles.instructionText}>
          Practice turning toward your partner instead of away
        </Text>
      </View>

      {data.turningTowardPractice.map((practice, index) => (
        <View key={index} style={styles.turningTowardCard}>
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>One bid my partner made:</Text>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., 'Look at this funny video' or 'I had a rough day'"
              value={practice.bidDescription}
              onChangeText={(text) => updateTurningTowardPractice(index, { bidDescription: text })}
              multiline
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>How I responded:</Text>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., 'Tell me more' or 'I\'m here for you'"
              value={practice.response}
              onChangeText={(text) => updateTurningTowardPractice(index, { response: text })}
              multiline
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>What changed when I turned toward them:</Text>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., They seemed more relaxed, We had a deeper conversation"
              value={practice.whatChanged}
              onChangeText={(text) => updateTurningTowardPractice(index, { whatChanged: text })}
              multiline
            />
          </View>
        </View>
      ))}
    </View>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return renderSuperheroDuoChart();
      case 1:
        return renderThriftShopShowdown();
      case 2:
        return renderChatPrompts();
      case 3:
        return renderTurningToward();
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View
        style={[styles.header, { backgroundColor: colors.purple }]}
      >
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Week 7: Superhero Duo</Text>
        <Text style={styles.headerSubtitle}>Discover your combined powers</Text>
      </View>

      {/* Progress Steps */}
      <View style={styles.progressContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {steps.map((step, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.progressStep,
                currentStep === index && styles.progressStepActive,
                data.completedSections[index] && styles.progressStepCompleted,
              ]}
              onPress={() => setCurrentStep(index)}
            >
              {data.completedSections[index] ? (
                <CheckCircle size={16} color={colors.white} />
              ) : (
                step.icon
              )}
              <Text style={[
                styles.progressStepText,
                currentStep === index && styles.progressStepTextActive,
                data.completedSections[index] && styles.progressStepTextCompleted,
              ]}>
                {step.title}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderCurrentStep()}
      </ScrollView>

      {/* Bottom Actions */}
      <View style={styles.bottomActions}>
        <TouchableOpacity style={styles.comeBackButton} onPress={handleComeBackLater}>
          <Text style={styles.comeBackButtonText}>Come Back Later</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.continueButton} onPress={handleSaveAndContinue}>
          <View
            style={[styles.continueButtonGradient, { backgroundColor: colors.purple }]}
          >
            <Text style={styles.continueButtonText}>
              {currentStep < steps.length - 1 ? 'Save & Continue' : 'Complete Week'}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundOrange,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 24,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: 20,
    zIndex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.white,
    marginBottom: 4,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 16,
    color: colors.white,
    opacity: 0.9,
    textAlign: 'center',
  },
  progressContainer: {
    backgroundColor: colors.white,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  progressStep: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 4,
    borderRadius: 20,
    backgroundColor: colors.backgroundTertiary,
  },
  progressStepActive: {
    backgroundColor: colors.purple,
  },
  progressStepCompleted: {
    backgroundColor: colors.success,
  },
  progressStepText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.textSecondary,
    marginLeft: 6,
  },
  progressStepTextActive: {
    color: colors.white,
  },
  progressStepTextCompleted: {
    color: colors.white,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  sectionContainer: {
    marginTop: 24,
  },
  sectionHeader: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    lineHeight: 24,
  },
  superheroCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  superheroTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 20,
    textAlign: 'center',
  },
  playerSection: {
    marginBottom: 24,
    padding: 16,
    backgroundColor: colors.backgroundGray,
    borderRadius: 12,
  },
  playerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.purple,
    marginBottom: 16,
    textAlign: 'center',
  },
  combinedSection: {
    padding: 16,
    backgroundColor: colors.backgroundOrange,
    borderRadius: 12,
  },
  combinedTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.orangeDark,
    marginBottom: 16,
    textAlign: 'center',
  },
  datePlanCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  datePlanTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 20,
    textAlign: 'center',
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: colors.borderMedium,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  chatPromptCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  promptText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 16,
    textAlign: 'center',
  },
  answerInputs: {
    gap: 16,
  },
  answerInput: {
    flex: 1,
  },
  playerLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  toolkitInstructions: {
    backgroundColor: colors.backgroundPink,
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  instructionText: {
    fontSize: 14,
    color: colors.purpleDark,
    marginBottom: 4,
    textAlign: 'center',
  },
  turningTowardCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  bottomActions: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
    gap: 12,
  },
  comeBackButton: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.borderMedium,
    alignItems: 'center',
  },
  comeBackButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textSecondary,
  },
  continueButton: {
    flex: 2,
    borderRadius: 12,
    overflow: 'hidden',
  },
  continueButtonGradient: {
    paddingVertical: 14,
    alignItems: 'center',
  },
  continueButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
});

import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';

import { Heart, BookOpen, User, Bell, Shield, HelpCircle, ArrowRight, Edit3, Lock, Eye, Settings } from 'lucide-react-native';
import { router } from 'expo-router';
import { useState } from 'react';
import PrivacyPolicy from '../components/PrivacyPolicy';
import Security from '../components/Security';
import { colors } from '../utils/colors';

export default function SettingsScreen() {
  const [hasOriginStory, setHasOriginStory] = useState(true); // This would come from storage
  const [showPrivacyPolicy, setShowPrivacyPolicy] = useState(false);
  const [showSecurity, setShowSecurity] = useState(false);

  const handleEditOriginStory = () => {
    // Navigate to origin story screen in edit mode
    router.push('/our-story?mode=edit');
  };

  const handleViewOriginStory = () => {
    // Navigate to the beautiful timeline view of the origin story
    router.push('/our-story');
  };

  const handlePrivacyPolicy = () => {
    setShowPrivacyPolicy(true);
  };

  const handleSecurity = () => {
    setShowSecurity(true);
  };

  const handleBackToSettings = () => {
    setShowPrivacyPolicy(false);
    setShowSecurity(false);
  };

  const settingsSections = [
    {
      title: 'Profile & Story',
      items: [
        {
          icon: <BookOpen size={20} color="colors.lightPink" />,
          title: 'Our Story',
          subtitle: hasOriginStory ? 'View and edit your origin story' : 'Share your love story',
          action: hasOriginStory ? handleViewOriginStory : handleEditOriginStory,
          showEdit: hasOriginStory,
        },
        {
          icon: <User size={20} color="colors.lightPurple" />,
          title: 'Couple Profile',
          subtitle: 'Edit names, icons, and preferences',
          action: () => router.push('/couple-profile'),
        },
      ],
    },
    {
      title: 'Privacy & Security',
      items: [
        {
          icon: <Eye size={20} color="colors.blue" />,
          title: 'Privacy Policy',
          subtitle: 'How we protect your data and privacy',
          action: handlePrivacyPolicy,
        },
        {
          icon: <Shield size={20} color="colors.accent1" />,
          title: 'Security',
          subtitle: 'Our security measures and data protection',
          action: handleSecurity,
        },
        {
          icon: <Lock size={20} color="colors.accent2" />,
          title: 'Data Control',
          subtitle: 'Manage your data and privacy settings',
          action: () => Alert.alert('Coming Soon', 'Data control settings will be available soon!'),
        },
      ],
    },
    {
      title: 'App Settings',
      items: [
        {
          icon: <Bell size={20} color="colors.blue" />,
          title: 'Notifications',
          subtitle: 'Manage reminder preferences',
          action: () => router.push('/notifications'),
        },
        {
          icon: <Settings size={20} color="colors.lightPurple" />,
          title: 'App Settings',
          subtitle: 'Customize your app experience',
          action: () => router.push('/app-settings'),
        },
        {
          icon: <Heart size={20} color="colors.lightPurple" />,
          title: 'Relationship Goals',
          subtitle: 'Set and track your relationship objectives',
          action: () => Alert.alert('Coming Soon', 'Relationship goals will be available soon!'),
        },
      ],
    },
    {
      title: 'Support',
      items: [
        {
          icon: <HelpCircle size={20} color="colors.accent2" />,
          title: 'Help & FAQ',
          subtitle: 'Get help and find answers',
          action: () => Alert.alert('Coming Soon', 'Help section will be available soon!'),
        },
        {
          icon: <Shield size={20} color="colors.accent3" />,
          title: 'About',
          subtitle: 'App version and information',
          action: () => Alert.alert('About', 'Everlasting Us v1.0.0\nYour haven for love, laughter, and growth'),
        },
      ],
    },
  ];

  // If showing privacy policy, render the PrivacyPolicy component
  if (showPrivacyPolicy) {
    return <PrivacyPolicy onBack={handleBackToSettings} />;
  }

  // If showing security, render the Security component
  if (showSecurity) {
    return <Security onBack={handleBackToSettings} />;
  }

  return (
    <View style={styles.container}>
      <View
        style={[styles.header, { backgroundColor: colors.primary }]}
      >
        <Text style={styles.headerTitle}>Settings</Text>
        <Text style={styles.headerSubtitle}>Customize your experience</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {settingsSections.map((section, sectionIndex) => (
          <View key={sectionIndex} style={styles.section}>
            <Text style={styles.sectionTitle}>{section.title}</Text>
            <View style={styles.sectionCard}>
              {section.items.map((item, itemIndex) => (
                <TouchableOpacity
                  key={itemIndex}
                  style={styles.settingItem}
                  onPress={item.action}
                >
                  <View style={styles.settingItemLeft}>
                    <View style={styles.settingIcon}>{item.icon}</View>
                    <View style={styles.settingText}>
                      <Text style={styles.settingTitle}>{item.title}</Text>
                      <Text style={styles.settingSubtitle}>{item.subtitle}</Text>
                    </View>
                  </View>
                  <View style={styles.settingItemRight}>
                    {item.showEdit && (
                      <TouchableOpacity
                        style={styles.editButton}
                        onPress={handleEditOriginStory}
                      >
                        <Edit3 size={16} color="colors.textSecondary" />
                      </TouchableOpacity>
                    )}
                    <ArrowRight size={20} color="colors.textTertiary" />
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        ))}

        <View style={styles.footer}>
          <Text style={styles.footerText}>
            Made with ❤️ for couples who believe in lasting love
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'colors.backgroundTertiary',
  },
  header: {
    paddingTop: 60,
    paddingBottom: 30,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: 'colors.white',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'colors.white',
    opacity: 0.9,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: 'colors.textSecondary',
    marginBottom: 12,
    marginLeft: 4,
  },
  sectionCard: {
    backgroundColor: 'colors.white',
    borderRadius: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'colors.backgroundTertiary',
  },
  settingItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'colors.backgroundTertiary',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: 'colors.textSecondary',
    marginBottom: 4,
  },
  settingSubtitle: {
    fontSize: 14,
    color: 'colors.textSecondary',
    lineHeight: 20,
  },
  settingItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  editButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'colors.backgroundTertiary',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  footerText: {
    fontSize: 14,
    color: 'colors.textTertiary',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

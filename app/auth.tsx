import React from 'react';
import { View, StyleSheet, TouchableOpacity, Text } from 'react-native';
import { router } from 'expo-router';
import { ArrowLeft } from 'lucide-react-native';
import AuthScreen from '../components/AuthScreen';
import { colors } from '../utils/colors';
import { DSHeaderBar } from '../components/shared';

export default function Auth() {
  return (
    <View style={styles.container}>
      <DSHeaderBar
        title="Create Account"
        tone="neutral"
        left={
          <TouchableOpacity onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.white} />
          </TouchableOpacity>
        }
      />

      <View style={styles.content}>
        <AuthScreen
          onAuthSuccess={() => {
            // Navigate to main app after successful auth
            router.replace('/(tabs)');
          }}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    backgroundColor: colors.background,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
  },
  content: {
    flex: 1,
  },
});

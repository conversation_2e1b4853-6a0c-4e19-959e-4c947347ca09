import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert } from 'react-native';

import { ArrowLeft, Save, CheckCircle, Heart, Users, MessageCircle, BookOpen, Calendar, MapPin, Shield, Crown, Star } from 'lucide-react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { useWeekNineData } from '../hooks/useWeekNineData';
import { colors } from '../utils/colors';

export default function WeekNineScreen() {
  const params = useLocalSearchParams();
  const [currentStep, setCurrentStep] = useState(0);
  
  // Handle URL parameters for direct section navigation
  useEffect(() => {
    if (params.section) {
      const sectionIndex = parseInt(params.section as string);
      if (sectionIndex >= 0 && sectionIndex < steps.length) {
        setCurrentStep(sectionIndex);
      }
    }
  }, [params.section]);
  
  const {
    data,
    updateCustomCrest,
    updateLiveShowDate,
    updateChatPrompt,
    updateSharedValues,
    updateCompletedSections,
  } = useWeekNineData();

  const steps = [
    { title: 'Create a Crest', icon: <Shield size={24} color={colors.white} /> },
    { title: 'Live Show Date', icon: <Calendar size={24} color={colors.white} /> },
    { title: 'Chat Prompts', icon: <MessageCircle size={24} color={colors.white} /> },
    { title: 'Shared Values', icon: <BookOpen size={24} color={colors.white} /> },
  ];

  const handleSaveAndContinue = () => {
    if (currentStep < steps.length - 1) {
      const newSections = [...data.completedSections];
      newSections[currentStep] = true;
      updateCompletedSections(newSections);
      setCurrentStep(currentStep + 1);
    } else {
      // All steps completed
      const newSections = [...data.completedSections];
      newSections[currentStep] = true;
      updateCompletedSections(newSections);
      Alert.alert('Congratulations!', 'You\'ve completed Week Nine! All your responses have been saved to your scrapbook.');
    }
  };

  const handleComeBackLater = () => {
    router.back();
  };

  const handleCrestInput = (field: 'icons' | 'colors' | 'symbols', index: number, value: string) => {
    const newArray = [...data.customCrest[field]];
    newArray[index] = value;
    updateCustomCrest({ [field]: newArray });
  };

  const handleValuesInput = (field: 'playerOneValues' | 'playerTwoValues' | 'sharedValues', index: number, value: string) => {
    const newArray = [...data.sharedValues[field]];
    newArray[index] = value;
    updateSharedValues({ [field]: newArray });
  };

  const renderCreateACrest = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Create a Crest</Text>
        <Text style={styles.sectionSubtitle}>Design your family crest with icons, colors, and symbols</Text>
      </View>

      <View style={styles.crestCard}>
        <Text style={styles.crestTitle}>Your Family Crest Design</Text>
        
        <View style={styles.crestSection}>
          <Text style={styles.crestSectionTitle}>Icons (3)</Text>
          {data.customCrest.icons.map((icon, index) => (
            <View key={index} style={styles.crestInput}>
              <Text style={styles.crestLabel}>{index + 1}.</Text>
              <TextInput
                style={styles.textInput}
                placeholder="e.g., Heart, Tree, Star, Crown..."
                value={icon}
                onChangeText={(text) => handleCrestInput('icons', index, text)}
              />
            </View>
          ))}
        </View>

        <View style={styles.crestSection}>
          <Text style={styles.crestSectionTitle}>Colors (3)</Text>
          {data.customCrest.colors.map((color, index) => (
            <View key={index} style={styles.crestInput}>
              <Text style={styles.crestLabel}>{index + 1}.</Text>
              <TextInput
                style={styles.textInput}
                placeholder="e.g., Gold, Blue, Red, Green..."
                value={color}
                onChangeText={(text) => handleCrestInput('colors', index, text)}
              />
            </View>
          ))}
        </View>

        <View style={styles.crestSection}>
          <Text style={styles.crestSectionTitle}>Symbols (3)</Text>
          {data.customCrest.symbols.map((symbol, index) => (
            <View key={index} style={styles.crestInput}>
              <Text style={styles.crestLabel}>{index + 1}.</Text>
              <TextInput
                style={styles.textInput}
                placeholder="e.g., Infinity, Anchor, Compass..."
                value={symbol}
                onChangeText={(text) => handleCrestInput('symbols', index, text)}
              />
            </View>
          ))}
        </View>

        <View style={styles.crestSection}>
          <Text style={styles.crestSectionTitle}>Design Notes</Text>
          <TextInput
            style={[styles.textInput, styles.designNotesInput, { minHeight: 80 }]}
            placeholder="Describe how these elements represent your relationship..."
            value={data.customCrest.designNotes}
            onChangeText={(text) => updateCustomCrest({ designNotes: text })}
            multiline
          />
        </View>

        <View style={styles.crestPreview}>
          <Text style={styles.crestPreviewTitle}>Crest Preview</Text>
          <View style={styles.crestPreviewContent}>
            <Shield size={48} color={colors.lime} />
            <Text style={styles.crestPreviewText}>
              {data.customCrest.icons.filter(i => i).join(' • ')} {'\n'}
              {data.customCrest.colors.filter(c => c).join(' • ')} {'\n'}
              {data.customCrest.symbols.filter(s => s).join(' • ')}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );

  const renderLiveShowDate = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Live Show Date</Text>
        <Text style={styles.sectionSubtitle}>Attend a live show together</Text>
      </View>

      <View style={styles.datePlanCard}>
        <Text style={styles.datePlanTitle}>Plan Your Live Show Date</Text>
        
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>When:</Text>
          <TextInput
            style={styles.textInput}
            placeholder="e.g., This Friday evening"
            value={data.liveShowDate.when}
            onChangeText={(text) => updateLiveShowDate({ when: text })}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Where:</Text>
          <TextInput
            style={styles.textInput}
            placeholder="e.g., Local theater, Concert hall"
            value={data.liveShowDate.where}
            onChangeText={(text) => updateLiveShowDate({ where: text })}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Type of Show:</Text>
          <TextInput
            style={styles.textInput}
            placeholder="e.g., Comedy show, Musical, Play, Concert"
            value={data.liveShowDate.typeOfShow}
            onChangeText={(text) => updateLiveShowDate({ typeOfShow: text })}
          />
        </View>
      </View>
    </View>
  );

  const renderChatPrompts = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Chat Prompts</Text>
        <Text style={styles.sectionSubtitle}>Deep conversations to strengthen your bond</Text>
      </View>

      {data.chatPrompts.map((prompt, index) => (
        <View key={index} style={styles.chatPromptCard}>
          <Text style={styles.promptText}>{prompt.prompt}</Text>
          
          <View style={styles.answerInputs}>
            <View style={styles.answerInput}>
              <Text style={styles.playerLabel}>Player One:</Text>
              <TextInput
                style={styles.textInput}
                placeholder="Your answer..."
                value={prompt.playerOneAnswer}
                onChangeText={(text) => updateChatPrompt(index, { playerOneAnswer: text })}
                multiline
              />
            </View>
            
            <View style={styles.answerInput}>
              <Text style={styles.playerLabel}>Player Two:</Text>
              <TextInput
                style={styles.textInput}
                placeholder="Your answer..."
                value={prompt.playerTwoAnswer}
                onChangeText={(text) => updateChatPrompt(index, { playerTwoAnswer: text })}
                multiline
              />
            </View>
          </View>
        </View>
      ))}
    </View>
  );

  const renderSharedValues = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Shared Values Statement</Text>
        <Text style={styles.sectionSubtitle}>Each lists 3-5 values, compare, choose shared ones</Text>
      </View>

      <View style={styles.valuesCard}>
        <View style={styles.valuesSection}>
          <Text style={styles.valuesSectionTitle}>Player One's Values</Text>
          {data.sharedValues.playerOneValues.map((value, index) => (
            <View key={index} style={styles.valueInput}>
              <Text style={styles.valueLabel}>{index + 1}.</Text>
              <TextInput
                style={styles.textInput}
                placeholder="e.g., Honesty, Adventure, Family..."
                value={value}
                onChangeText={(text) => handleValuesInput('playerOneValues', index, text)}
              />
            </View>
          ))}
        </View>

        <View style={styles.valuesSection}>
          <Text style={styles.valuesSectionTitle}>Player Two's Values</Text>
          {data.sharedValues.playerTwoValues.map((value, index) => (
            <View key={index} style={styles.valueInput}>
              <Text style={styles.valueLabel}>{index + 1}.</Text>
              <TextInput
                style={styles.textInput}
                placeholder="e.g., Loyalty, Creativity, Growth..."
                value={value}
                onChangeText={(text) => handleValuesInput('playerTwoValues', index, text)}
              />
            </View>
          ))}
        </View>

        <View style={styles.valuesSection}>
          <Text style={styles.valuesSectionTitle}>Your Shared Values (3-5)</Text>
          {data.sharedValues.sharedValues.map((value, index) => (
            <View key={index} style={styles.valueInput}>
              <Text style={styles.valueLabel}>{index + 1}.</Text>
              <TextInput
                style={styles.textInput}
                placeholder="Choose values you both share..."
                value={value}
                onChangeText={(text) => handleValuesInput('sharedValues', index, text)}
              />
            </View>
          ))}
        </View>

        <View style={styles.valuesSection}>
          <Text style={styles.valuesSectionTitle}>Shared Values Statement</Text>
          <Text style={styles.valuesStatementSubtitle}>
            Write 2-5 sentences about what these shared values mean for your relationship
          </Text>
          <TextInput
            style={[styles.textInput, styles.valuesStatementInput, { minHeight: 100 }]}
            placeholder="Our shared values of [value1], [value2], and [value3] mean that we..."
            value={data.sharedValues.valuesStatement}
            onChangeText={(text) => updateSharedValues({ valuesStatement: text })}
            multiline
          />
        </View>
      </View>
    </View>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return renderCreateACrest();
      case 1:
        return renderLiveShowDate();
      case 2:
        return renderChatPrompts();
      case 3:
        return renderSharedValues();
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View
        style={[styles.header, { backgroundColor: colors.lime }]}
      >
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Week 9: Custom Crest</Text>
        <Text style={styles.headerSubtitle}>Design your family crest and explore shared values</Text>
      </View>

      {/* Progress Steps */}
      <View style={styles.progressContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {steps.map((step, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.progressStep,
                currentStep === index && styles.progressStepActive,
                data.completedSections[index] && styles.progressStepCompleted,
              ]}
              onPress={() => setCurrentStep(index)}
            >
              {data.completedSections[index] ? (
                <CheckCircle size={16} color={colors.white} />
              ) : (
                step.icon
              )}
              <Text style={[
                styles.progressStepText,
                currentStep === index && styles.progressStepTextActive,
                data.completedSections[index] && styles.progressStepTextCompleted,
              ]}>
                {step.title}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderCurrentStep()}
      </ScrollView>

      {/* Bottom Actions */}
      <View style={styles.bottomActions}>
        <TouchableOpacity style={styles.comeBackButton} onPress={handleComeBackLater}>
          <Text style={styles.comeBackButtonText}>Come Back Later</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.continueButton} onPress={handleSaveAndContinue}>
          <View
            style={[styles.continueButtonGradient, { backgroundColor: colors.lime }]}
          >
            <Text style={styles.continueButtonText}>
              {currentStep < steps.length - 1 ? 'Save & Continue' : 'Complete Week'}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundOrange,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 24,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: 20,
    zIndex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.white,
    marginBottom: 4,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 16,
    color: colors.white,
    opacity: 0.9,
    textAlign: 'center',
  },
  progressContainer: {
    backgroundColor: colors.white,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  progressStep: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 4,
    borderRadius: 20,
    backgroundColor: colors.backgroundTertiary,
  },
  progressStepActive: {
    backgroundColor: colors.lime,
  },
  progressStepCompleted: {
    backgroundColor: colors.success,
  },
  progressStepText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.textSecondary,
    marginLeft: 6,
  },
  progressStepTextActive: {
    color: colors.white,
  },
  progressStepTextCompleted: {
    color: colors.white,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  sectionContainer: {
    marginTop: 24,
  },
  sectionHeader: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    lineHeight: 24,
  },
  crestCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  crestTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 20,
    textAlign: 'center',
  },
  crestSection: {
    marginBottom: 24,
  },
  crestSectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.lime,
    marginBottom: 16,
    textAlign: 'center',
  },
  crestInput: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  crestLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginRight: 12,
    minWidth: 20,
  },
  textInput: {
    borderWidth: 1,
    borderColor: colors.borderMedium,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    flex: 1,
  },
  designNotesInput: {
    minHeight: 80,
  },
  crestPreview: {
    padding: 16,
    backgroundColor: colors.backgroundGray,
    borderRadius: 12,
    alignItems: 'center',
  },
  crestPreviewTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.green,
    marginBottom: 16,
  },
  crestPreviewContent: {
    alignItems: 'center',
  },
  crestPreviewText: {
    fontSize: 14,
    color: colors.textPrimary,
    textAlign: 'center',
    marginTop: 8,
    lineHeight: 20,
  },
  datePlanCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  datePlanTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 20,
    textAlign: 'center',
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  chatPromptCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  promptText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 16,
    textAlign: 'center',
  },
  answerInputs: {
    gap: 16,
  },
  answerInput: {
    flex: 1,
  },
  playerLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  valuesCard: {
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  valuesSection: {
    marginBottom: 24,
  },
  valuesSectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.lime,
    marginBottom: 16,
    textAlign: 'center',
  },
  valueInput: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  valueLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginRight: 12,
    minWidth: 20,
  },
  valuesStatementSubtitle: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 12,
    textAlign: 'center',
    lineHeight: 20,
  },
  valuesStatementInput: {
    minHeight: 100,
  },
  bottomActions: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
    gap: 12,
  },
  comeBackButton: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.borderMedium,
    alignItems: 'center',
  },
  comeBackButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textSecondary,
  },
  continueButton: {
    flex: 2,
    borderRadius: 12,
    overflow: 'hidden',
  },
  continueButtonGradient: {
    paddingVertical: 14,
    alignItems: 'center',
  },
  continueButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
});

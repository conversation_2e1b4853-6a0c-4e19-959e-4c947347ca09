import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert, Modal } from 'react-native';

import { ArrowLeft, Save, CheckCircle, Heart, Users, MessageCircle, BookOpen, Play, Calendar, MapPin, Star, X } from 'lucide-react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { useWeekTwoData } from '../hooks/useWeekTwoData';
import { usePointsSystemSupabase } from '../hooks/usePointsSystemSupabase';
import { colors } from '../utils/colors';

export default function WeekTwoScreen() {
  const params = useLocalSearchParams();
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedBingoSquare, setSelectedBingoSquare] = useState<number | null>(null);
  const [showExampleModal, setShowExampleModal] = useState(false);
  
  // Handle URL parameters for direct section navigation
  useEffect(() => {
    if (params.section) {
      const sectionIndex = parseInt(params.section as string);
      if (sectionIndex >= 0 && sectionIndex < steps.length) {
        setCurrentStep(sectionIndex);
      }
    }
  }, [params.section]);
  
  const {
    data,
    updateStrengthsBingo,
    updateDateNightPlan,
    updateChatPrompt,
    updateFiveToOneRatio,
    updateCompletedSections,
  } = useWeekTwoData();

  const { addPoints } = usePointsSystemSupabase();

  const steps = [
    { title: 'Strengths Bingo', icon: <Star size={24} color={colors.white} />, activityName: 'Strengths Bingo' },
    { title: 'Date Night Plan', icon: <Calendar size={24} color={colors.white} />, activityName: 'Date Night Plan' },
    { title: 'Chat Prompts', icon: <MessageCircle size={24} color={colors.white} />, activityName: 'Chat Prompts' },
    { title: '5:1 Ratio', icon: <Heart size={24} color={colors.white} />, activityName: '5:1 Ratio' },
  ];

  const handleSaveAndContinue = async () => {
    try {
      // Mark current activity as completed and award points
      const result = await addPoints(25, 'activity');
      
      if (currentStep < steps.length - 1) {
        const newSections = [...data.completedSections];
        newSections[currentStep] = true;
        updateCompletedSections(newSections);
        setCurrentStep(currentStep + 1);
        
        // Show points earned
        Alert.alert(
          'Activity Completed!', 
          `Great job! You earned ${result.points} points!${result.bonusPoints > 0 ? `\n\nModule completion bonus: +${result.bonusPoints} points!` : ''}`
        );
      } else {
        // All steps completed
        const newSections = [...data.completedSections];
        newSections[currentStep] = true;
        updateCompletedSections(newSections);
        
        const finalResult = await addPoints(25, 'activity');
        Alert.alert(
          'Congratulations!', 
          `You've completed Week Two! All your responses have been saved to your scrapbook.\n\nFinal activity: +25 points!`
        );
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to save progress. Please try again.');
    }
  };

  const handleComeBackLater = () => {
    router.back();
  };

  const handleBingoSquarePress = (index: number) => {
    setSelectedBingoSquare(index);
    setShowExampleModal(true);
  };

  const handleSaveExample = async (example: string) => {
    if (selectedBingoSquare !== null) {
      updateStrengthsBingo(selectedBingoSquare, { 
        isSelected: true, 
        example,
        timestamp: Date.now()
      });
      setShowExampleModal(false);
      setSelectedBingoSquare(null);
      
      // Check if this completes the Strengths Bingo activity
      const selectedCount = data.strengthsBingo.filter(s => s.isSelected).length;
      if (selectedCount >= 5) { // Assuming 5 selected squares completes the activity
        try {
          const result = await addPoints(25, 'activity');
          Alert.alert(
            'Strengths Bingo Completed!', 
            `Great job! You earned 25 points!`
          );
        } catch (error) {
          console.log('Error completing activity:', error);
        }
      }
    }
  };

  // Check if current step is completed
  const isStepCompleted = (stepIndex: number) => {
    return data.completedSections[stepIndex];
  };

  const renderStrengthsBingo = () => {
    const selectedCount = data.strengthsBingo.filter(square => square.isSelected).length;
    
    return (
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Strengths Bingo</Text>
          <Text style={styles.sectionSubtitle}>Celebrate your partner's strengths!</Text>
        </View>

        <View style={styles.bingoInstructions}>
          <Text style={styles.instructionText}>
            Each partner takes turns sharing examples of when the other demonstrated a strength.
          </Text>
          <Text style={styles.instructionText}>
            Check off traits on the grid as they're acknowledged. Aim to complete a row, column, or diagonal!
          </Text>
        </View>

        <View style={styles.bingoGrid}>
          {data.strengthsBingo.map((square, index) => (
            <TouchableOpacity
              key={square.id}
              style={[
                styles.bingoSquare,
                square.isSelected && styles.bingoSquareSelected
              ]}
              onPress={() => handleBingoSquarePress(index)}
            >
              <Text style={[
                styles.bingoSquareText,
                square.isSelected && styles.bingoSquareTextSelected
              ]}>
                {square.strength}
              </Text>
              {square.isSelected && (
                <View style={styles.checkmark}>
                  <CheckCircle size={16} color={colors.white} />
                </View>
              )}
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.bingoProgress}>
          <Text style={styles.progressText}>
            {selectedCount} of 25 strengths celebrated!
          </Text>
          {selectedCount > 0 && (
            <Text style={styles.progressSubtext}>
              Keep going! You're building a beautiful picture of your love.
            </Text>
          )}
        </View>

        {/* Example Modal */}
        <Modal
          visible={showExampleModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowExampleModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>
                  {selectedBingoSquare !== null ? data.strengthsBingo[selectedBingoSquare].strength : ''}
                </Text>
                <TouchableOpacity onPress={() => setShowExampleModal(false)}>
                  <X size={24} color={colors.textSecondary} />
                </TouchableOpacity>
              </View>
              
              <Text style={styles.modalSubtitle}>
                Share a specific example of when your partner showed this strength:
              </Text>
              
              <TextInput
                style={styles.exampleInput}
                placeholder="Describe the moment..."
                value={selectedBingoSquare !== null ? data.strengthsBingo[selectedBingoSquare].example : ''}
                onChangeText={(text) => {
                  if (selectedBingoSquare !== null) {
                    updateStrengthsBingo(selectedBingoSquare, { example: text });
                  }
                }}
                multiline
                numberOfLines={4}
              />
              
              <TouchableOpacity 
                style={styles.saveExampleButton}
                onPress={() => {
                  if (selectedBingoSquare !== null) {
                    handleSaveExample(data.strengthsBingo[selectedBingoSquare].example);
                  }
                }}
              >
                <View
                  style={[styles.saveExampleButtonGradient, { backgroundColor: colors.solidColors.moduleLightPink }]}
                >
                  <Text style={styles.saveExampleButtonText}>Save & Celebrate!</Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
      </View>
    );
  };

  const renderDateNightPlan = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Date Night Plan</Text>
        <Text style={styles.sectionSubtitle}>Cooking Showdown – Plate It Like a Pro</Text>
      </View>

      <View style={styles.planCard}>
        <Text style={styles.planDescription}>
          Team up to make a full meal. Each person picks one part (main, side, or garnish). 
          Focus on presentation — winner is the most beautiful and creative plate.
        </Text>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>When?</Text>
          <TextInput
            style={styles.textInput}
            placeholder="e.g., Saturday at 6 PM"
            value={data.dateNightPlan.when}
            onChangeText={(text) => updateDateNightPlan({ when: text })}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Where?</Text>
          <TextInput
            style={styles.textInput}
            placeholder="e.g., Our kitchen"
            value={data.dateNightPlan.where}
            onChangeText={(text) => updateDateNightPlan({ where: text })}
          />
        </View>

        <TouchableOpacity
          style={styles.completeButton}
          onPress={() => updateDateNightPlan({ completed: !data.dateNightPlan.completed })}
        >
          <View
            style={[styles.completeButtonGradient, { backgroundColor: data.dateNightPlan.completed ? colors.solidColors.moduleGreen : colors.solidColors.moduleLightPink }]}
          >
            {data.dateNightPlan.completed ? <CheckCircle size={20} color={colors.white} /> : <Play size={20} color={colors.white} />}
            <Text style={styles.completeButtonText}>
              {data.dateNightPlan.completed ? 'Completed!' : 'Mark as Completed'}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderChatPrompts = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Date Night Chat Prompts</Text>
        <Text style={styles.sectionSubtitle}>Fun conversations over your cooking creation</Text>
      </View>

      {data.chatPrompts.map((prompt, index) => (
        <View key={index} style={styles.promptCard}>
          <Text style={styles.promptText}>{prompt.prompt}</Text>
          
          <View style={styles.promptInputs}>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Player One</Text>
              <TextInput
                style={styles.textInput}
                placeholder="Your answer..."
                value={prompt.playerOneAnswer}
                onChangeText={(text) => updateChatPrompt(index, { playerOneAnswer: text })}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Player Two</Text>
              <TextInput
                style={styles.textInput}
                placeholder="Your answer..."
                value={prompt.playerTwoAnswer}
                onChangeText={(text) => updateChatPrompt(index, { playerTwoAnswer: text })}
              />
            </View>
          </View>
        </View>
      ))}
    </View>
  );

  const renderFiveToOneRatio = () => (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Relationship Skills Toolkit</Text>
        <Text style={styles.sectionSubtitle}>The 5:1 Positive-to-Negative Ratio</Text>
      </View>

      <View style={styles.skillCard}>
        <Text style={styles.skillIntro}>
          Strong relationships have at least 5 positive interactions for every 1 negative. 
          Look for small daily ways to add positivity — gratitude, laughter, kind words, or affection.
        </Text>

        <Text style={styles.practiceTitle}>Practice Time:</Text>
        <Text style={styles.practiceSubtitle}>
          Write down 5 feel-good moments from the past few days:
        </Text>

        {data.fiveToOneRatio.positiveMoments.map((moment, index) => (
          <View key={index} style={styles.momentInput}>
            <Text style={styles.momentLabel}>Moment {index + 1}</Text>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., You made me laugh at breakfast..."
              value={moment}
              onChangeText={(text) => updateFiveToOneRatio(index, text)}
              multiline
            />
          </View>
        ))}

        <View style={styles.ratioTip}>
          <Text style={styles.ratioTipText}>
            💡 Tip: Try to repeat this exercise regularly to maintain your positive ratio!
          </Text>
        </View>
      </View>
    </View>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return renderStrengthsBingo();
      case 1:
        return renderDateNightPlan();
      case 2:
        return renderChatPrompts();
      case 3:
        return renderFiveToOneRatio();
      default:
        return null;
    }
  };

  const completedCount = data.completedSections.filter(Boolean).length;

  return (
    <View style={styles.container}>
      {/* Header */}
      <View
        style={[styles.header, { backgroundColor: colors.secondary }]}
      >
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.white} />
        </TouchableOpacity>
        
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Week Two</Text>
          <Text style={styles.headerSubtitle}>Strengths Bingo</Text>
        </View>

        <View style={styles.progressBadge}>
          <Text style={styles.progressBadgeText}>{completedCount}/4</Text>
        </View>
      </View>

      {/* Progress Steps */}
      <View style={styles.stepsContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {steps.map((step, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.stepIndicator,
                index === currentStep && styles.stepIndicatorActive,
                isStepCompleted(index) && styles.stepIndicatorCompleted
              ]}
              onPress={() => setCurrentStep(index)}
            >
              {isStepCompleted(index) ? (
                <CheckCircle size={16} color={colors.white} />
              ) : (
                step.icon
              )}
              <Text style={[
                styles.stepText,
                index === currentStep && styles.stepTextActive,
                isStepCompleted(index) && styles.stepTextCompleted
              ]}>
                {step.title}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderCurrentStep()}
      </ScrollView>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <TouchableOpacity style={styles.comeBackButton} onPress={handleComeBackLater}>
          <Text style={styles.comeBackButtonText}>Come Back Later</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.saveButton} onPress={handleSaveAndContinue}>
          <View
            style={[styles.saveButtonGradient, { backgroundColor: colors.secondary }]}
          >
            <Save size={20} color={colors.white} />
            <Text style={styles.saveButtonText}>
              {currentStep < steps.length - 1 ? 'Save & Continue' : 'Complete Week Two!'}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundOrange,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: 8,
  },
  headerContent: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.white,
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 16,
    color: colors.white,
    opacity: 0.9,
  },
  progressBadge: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  progressBadgeText: {
    color: colors.white,
    fontSize: 14,
    fontWeight: '600',
  },
  stepsContainer: {
    backgroundColor: 'colors.white',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'colors.backgroundTertiary',
  },
  stepIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 4,
    borderRadius: 20,
    backgroundColor: 'colors.backgroundTertiary',
  },
  stepIndicatorActive: {
    backgroundColor: 'colors.lightPurple',
  },
  stepIndicatorCompleted: {
    backgroundColor: colors.success,
  },
  stepText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
    color: 'colors.textSecondary',
  },
  stepTextActive: {
    color: colors.white,
  },
  stepTextCompleted: {
    color: colors.white,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  sectionContainer: {
    marginTop: 20,
    marginBottom: 40,
  },
  sectionHeader: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: 'colors.textPrimary',
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 16,
    color: 'colors.textSecondary',
  },
  bingoInstructions: {
    backgroundColor: colors.backgroundOrange,
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  instructionText: {
    fontSize: 14,
    color: 'colors.textSecondary',
    textAlign: 'center',
    marginBottom: 4,
  },
  bingoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  bingoSquare: {
    width: '18%',
    aspectRatio: 1,
    backgroundColor: 'colors.white',
    borderRadius: 12,
    padding: 8,
    marginBottom: 8,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    position: 'relative',
  },
  bingoSquareSelected: {
    backgroundColor: 'colors.lightPurple',
  },
  bingoSquareText: {
    fontSize: 10,
    fontWeight: '600',
    color: 'colors.textPrimary',
    textAlign: 'center',
    lineHeight: 12,
  },
  bingoSquareTextSelected: {
    color: colors.white,
  },
  checkmark: {
    position: 'absolute',
    top: 4,
    right: 4,
  },
  bingoProgress: {
    alignItems: 'center',
    marginTop: 16,
  },
  progressText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'colors.textPrimary',
    marginBottom: 4,
  },
  progressSubtext: {
    fontSize: 14,
    color: 'colors.textSecondary',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    backgroundColor: 'colors.white',
    borderRadius: 16,
    padding: 24,
    width: '100%',
    maxWidth: 400,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: 'colors.textPrimary',
    flex: 1,
  },
  modalSubtitle: {
    fontSize: 14,
    color: 'colors.textSecondary',
    marginBottom: 16,
    lineHeight: 20,
  },
  exampleInput: {
    borderWidth: 1,
    borderColor: 'colors.borderLight',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    minHeight: 100,
    textAlignVertical: 'top',
    backgroundColor: 'colors.backgroundGray',
    marginBottom: 20,
  },
  saveExampleButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  saveExampleButtonGradient: {
    padding: 14,
    alignItems: 'center',
  },
  saveExampleButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  planCard: {
    backgroundColor: 'colors.white',
    padding: 20,
    borderRadius: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  planDescription: {
    fontSize: 16,
    color: 'colors.textSecondary',
    lineHeight: 24,
    marginBottom: 20,
    textAlign: 'center',
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: 'colors.textPrimary',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: 'colors.borderLight',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    backgroundColor: 'colors.backgroundGray',
  },
  completeButton: {
    borderRadius: 12,
    overflow: 'hidden',
    marginTop: 8,
  },
  completeButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 14,
  },
  completeButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  promptCard: {
    backgroundColor: 'colors.white',
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  promptText: {
    fontSize: 18,
    fontWeight: '600',
    color: 'colors.textPrimary',
    marginBottom: 16,
    textAlign: 'center',
  },
  promptInputs: {
    gap: 16,
  },
  skillCard: {
    backgroundColor: 'colors.white',
    padding: 20,
    borderRadius: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  skillIntro: {
    fontSize: 16,
    color: 'colors.textSecondary',
    lineHeight: 24,
    marginBottom: 20,
    textAlign: 'center',
  },
  practiceTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: 'colors.textPrimary',
    marginBottom: 8,
  },
  practiceSubtitle: {
    fontSize: 14,
    color: 'colors.textSecondary',
    marginBottom: 16,
  },
  momentInput: {
    marginBottom: 16,
  },
  momentLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: 'colors.textPrimary',
    marginBottom: 8,
  },
  ratioTip: {
    backgroundColor: colors.backgroundOrange,
    padding: 16,
    borderRadius: 12,
    marginTop: 16,
  },
  ratioTipText: {
    fontSize: 14,
    color: 'colors.textSecondary',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  actionButtons: {
    flexDirection: 'row',
    padding: 20,
    gap: 12,
    backgroundColor: 'colors.white',
    borderTopWidth: 1,
    borderTopColor: 'colors.backgroundTertiary',
  },
  comeBackButton: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'colors.borderLight',
    alignItems: 'center',
  },
  comeBackButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'colors.textSecondary',
  },
  saveButton: {
    flex: 2,
    borderRadius: 12,
    overflow: 'hidden',
  },
  saveButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  saveButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});

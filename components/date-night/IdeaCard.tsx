import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert
} from 'react-native';

import {
  Heart,
  Calendar,
  Trash2,
  Edit3,
  CheckCircle,
  Clock,
  DollarSign,
  MapPin
} from 'lucide-react-native';
import { colors } from '../../utils/colors';
import {
  DateNightIdeaGlobal,
  DateNightIdeaUser,
  CostLevel,
  DifficultyLevel,
  LocationType
} from '../../types/supabase';

interface IdeaCardProps {
  idea: DateNightIdeaGlobal | DateNightIdeaUser;
  isUserIdea?: boolean;
  userStatus?: 'favorite' | 'planned' | 'completed';
  onPlan?: (idea: DateNightIdeaGlobal | DateNightIdeaUser) => void;
  onSave?: (idea: DateNightIdeaGlobal | DateNightIdeaUser) => void;
  onEdit?: (idea: DateNightIdeaUser) => void;
  onDelete?: (idea: DateNightIdeaUser) => void;
  onComplete?: (idea: DateNightIdeaUser) => void;
}

export default function IdeaCard({
  idea,
  isUserIdea = false,
  userStatus,
  onPlan,
  onSave,
  onEdit,
  onDelete,
  onComplete
}: IdeaCardProps) {
  const isGlobalIdea = 'source' in idea;
  const isWeekly = isGlobalIdea && idea.source === 'weekly';

  const handlePlan = () => {
    if (onPlan) {
      onPlan(idea);
    }
  };

  const handleSave = () => {
    if (onSave) {
      onSave(idea);
    }
  };

  const handleEdit = () => {
    if (onEdit && isUserIdea) {
      onEdit(idea as DateNightIdeaUser);
    }
  };

  const handleDelete = () => {
    if (onDelete && isUserIdea) {
      Alert.alert(
        'Delete Idea',
        'Are you sure you want to delete this idea?',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Delete',
            style: 'destructive',
            onPress: () => onDelete(idea as DateNightIdeaUser)
          }
        ]
      );
    }
  };

  const handleComplete = () => {
    if (onComplete && isUserIdea) {
      onComplete(idea as DateNightIdeaUser);
    }
  };

  const getCostIcon = (cost: CostLevel) => {
    switch (cost) {
      case 'free':
        return <DollarSign size={12} color={colors.success} />;
      case 'low':
        return <DollarSign size={12} color={colors.warning} />;
      case 'medium':
        return <DollarSign size={12} color={colors.primary} />;
      case 'high':
        return <DollarSign size={12} color={colors.error} />;
      default:
        return null;
    }
  };

  const getLocationIcon = (location: LocationType) => {
    switch (location) {
      case 'indoor':
        return <MapPin size={12} color={colors.primary} />;
      case 'outdoor':
        return <MapPin size={12} color={colors.success} />;
      case 'both':
        return <MapPin size={12} color={colors.warning} />;
      default:
        return null;
    }
  };

  const getDifficultyColor = (difficulty: DifficultyLevel) => {
    switch (difficulty) {
      case 'easy':
        return colors.success;
      case 'medium':
        return colors.warning;
      case 'hard':
        return colors.error;
      default:
        return colors.primary;
    }
  };

  const getCardColor = () => {
    if (isWeekly) {
      return colors.primary;
    }
    if (isUserIdea) {
      return colors.secondary;
    }
    return colors.accent2;
  };

  return (
    <View style={styles.card}>
      <View
        style={[styles.cardGradient, { backgroundColor: getCardColor() }]}
      >
        <View style={styles.cardHeader}>
          <View style={styles.cardTitleRow}>
            {idea.emoji && <Text style={styles.emoji}>{idea.emoji}</Text>}
            <Text style={styles.cardTitle}>{idea.title}</Text>
            {isWeekly && 'week_number' in idea && idea.week_number && (
              <View style={styles.weekBadge}>
                <Text style={styles.weekBadgeText}>Week {idea.week_number}</Text>
              </View>
            )}
          </View>
          {isUserIdea && (
            <View style={styles.userActions}>
              {onEdit && (
                <TouchableOpacity
                  onPress={handleEdit}
                  style={styles.actionButton}
                >
                  <Edit3 size={16} color={colors.white} />
                </TouchableOpacity>
              )}
              {onDelete && (
                <TouchableOpacity
                  onPress={handleDelete}
                  style={styles.actionButton}
                >
                  <Trash2 size={16} color={colors.white} />
                </TouchableOpacity>
              )}
            </View>
          )}
        </View>

        <Text style={styles.cardDescription}>{idea.description}</Text>

        <View style={styles.cardFooter}>
          <View style={styles.badges}>
            {idea.category && (
              <View style={styles.badge}>
                <Text style={styles.badgeText}>{idea.category}</Text>
              </View>
            )}
            {idea.difficulty && (
              <View style={[styles.badge, { backgroundColor: getDifficultyColor(idea.difficulty) }]}>
                <Text style={styles.badgeText}>{idea.difficulty}</Text>
              </View>
            )}
          </View>

          <View style={styles.metaInfo}>
            {idea.estimated_duration && (
              <View style={styles.metaItem}>
                <Clock size={12} color={colors.white} />
                <Text style={styles.metaText}>{idea.estimated_duration}min</Text>
              </View>
            )}
            {idea.cost && (
              <View style={styles.metaItem}>
                {getCostIcon(idea.cost)}
                <Text style={styles.metaText}>{idea.cost}</Text>
              </View>
            )}
            {idea.indoor_outdoor && (
              <View style={styles.metaItem}>
                {getLocationIcon(idea.indoor_outdoor)}
                <Text style={styles.metaText}>{idea.indoor_outdoor}</Text>
              </View>
            )}
          </View>
        </View>

        {!isUserIdea && (
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={styles.planButton}
              onPress={handlePlan}
            >
              <Calendar size={16} color={colors.white} />
              <Text style={styles.planButtonText}>Plan This</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.saveButton,
                userStatus === 'favorite' && styles.savedButton
              ]}
              onPress={handleSave}
            >
              <Heart size={16} color={colors.white} />
              <Text style={styles.saveButtonText}>
                {userStatus === 'favorite' ? 'Saved' : 'Save'}
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {isUserIdea && userStatus === 'completed' && (
          <View style={styles.completedInfo}>
            <CheckCircle size={16} color={colors.success} />
            <Text style={styles.completedText}>Completed!</Text>
          </View>
        )}

        {isUserIdea && userStatus !== 'completed' && onComplete && (
          <TouchableOpacity
            style={styles.completeButton}
            onPress={handleComplete}
          >
            <CheckCircle size={16} color={colors.white} />
            <Text style={styles.completeButtonText}>Mark Complete</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  cardGradient: {
    padding: 20,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  cardTitleRow: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  emoji: {
    fontSize: 24,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.white,
    flex: 1,
  },
  weekBadge: {
    backgroundColor: colors.white,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  weekBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.primary,
  },
  userActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    padding: 4,
  },
  cardDescription: {
    fontSize: 14,
    color: colors.white,
    lineHeight: 20,
    marginBottom: 12,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  badges: {
    flexDirection: 'row',
    gap: 8,
  },
  badge: {
    backgroundColor: colors.white,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.primary,
  },
  metaInfo: {
    flexDirection: 'row',
    gap: 12,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metaText: {
    fontSize: 12,
    color: colors.white,
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  planButton: {
    flex: 1,
    backgroundColor: colors.white,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 6,
  },
  planButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.primary,
  },
  saveButton: {
    flex: 1,
    backgroundColor: colors.white,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 6,
  },
  savedButton: {
    backgroundColor: colors.success,
  },
  saveButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.primary,
  },
  completedInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginTop: 12,
  },
  completedText: {
    fontSize: 14,
    color: colors.white,
    fontWeight: '500',
  },
  completeButton: {
    backgroundColor: colors.success,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 6,
    marginTop: 12,
  },
  completeButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.white,
  },
});

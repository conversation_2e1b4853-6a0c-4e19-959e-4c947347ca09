import React from 'react';
import { View, Text, StyleSheet, ViewStyle, Image } from 'react-native';
import { colors } from '../../utils/colors';

// ProfileHeader component: shows two circular avatars, a heart between, names and subtitle
export interface ProfileHeaderProps {
  names: string;
  subtitle?: string;
  leftEmoji?: string;   // fallback visual if no image
  rightEmoji?: string;  // fallback visual if no image
  leftImage?: string;   // profile image URI for left partner
  rightImage?: string;  // profile image URI for right partner
  style?: ViewStyle;
}

export const ProfileHeader: React.FC<ProfileHeaderProps> = ({
  names,
  subtitle,
  leftEmoji = '👩🏻‍🦰',
  rightEmoji = '👨🏻‍🦱',
  leftImage,
  rightImage,
  style,
}) => {
  return (
    <View style={[styles.profileHeader, style]}>
      <View style={styles.profilePicsContainer}>
        <View style={[styles.profilePic, styles.profilePicSolid]}>
          {leftImage ? (
            <Image source={{ uri: leftImage }} style={styles.profileImage} />
          ) : (
            <Text style={styles.profileEmoji}>{leftEmoji}</Text>
          )}
        </View>
        <View style={styles.connectionHeart}>
          <Text style={styles.heartEmoji}>💖</Text>
        </View>
        <View style={[styles.profilePic, styles.profilePicSolid]}>
          {rightImage ? (
            <Image source={{ uri: rightImage }} style={styles.profileImage} />
          ) : (
            <Text style={styles.profileEmoji}>{rightEmoji}</Text>
          )}
        </View>
      </View>
      <Text style={styles.coupleNames}>{names}</Text>
      {subtitle ? <Text style={styles.relationshipStatus}>{subtitle}</Text> : null}
    </View>
  );
};

// StatsOverview component: 2x2 grid stats matching the new style
export interface StatItem {
  emoji: string;
  number: number | string;
  label: string;
  iconBackgroundColor?: string; // defaults to brand solids depending on position
}

export interface StatsOverviewProps {
  title?: string;
  items: StatItem[]; // Supply 2 or 4 for rows
  style?: ViewStyle;
}

export const StatsOverview: React.FC<StatsOverviewProps> = ({ title = 'Your Journey Together', items, style }) => {
  // Split items into rows of two
  const rows: StatItem[][] = [];
  for (let i = 0; i < items.length; i += 2) rows.push(items.slice(i, i + 2));

  return (
    <View style={[styles.statsOverview, style]}>
      <Text style={styles.statsTitle}>{title}</Text>
      {rows.map((row, idx) => (
        <View key={idx} style={styles.statsGrid}>
          {row.map((item, j) => (
            <View key={`${idx}-${j}`} style={styles.statItem}>
              <View style={[styles.statIcon, { backgroundColor: item.iconBackgroundColor || colors.darkerPink }]}>
                <Text style={styles.statIconEmoji}>{item.emoji}</Text>
              </View>
              <Text style={styles.statNumber}>{item.number}</Text>
              <Text style={styles.statLabel}>{item.label}</Text>
            </View>
          ))}
        </View>
      ))}
    </View>
  );
};

// FavoritesSection component: list of favorite items with emoji
export interface FavoriteItem { emoji: string; text: string }
export interface FavoritesSectionProps { title?: string; items: FavoriteItem[]; style?: ViewStyle }
export const FavoritesSection: React.FC<FavoritesSectionProps> = ({ title = 'Favorite Activities', items, style }) => {
  return (
    <View style={[styles.favoritesSection, style]}>
      <Text style={styles.favoritesTitle}>{title}</Text>
      {items.map((f, idx) => (
        <View key={idx} style={[styles.favoriteItem, idx === items.length - 1 && styles.favoriteItemLast]}> 
          <Text style={styles.favoriteEmoji}>{f.emoji}</Text>
          <Text style={styles.favoriteText}>{f.text}</Text>
        </View>
      ))}
    </View>
  );
};

// AchievementsSection component
export interface AchievementItem { emoji: string; name: string; desc: string }
export interface AchievementsSectionProps { title?: string; items: AchievementItem[]; style?: ViewStyle }
export const AchievementsSection: React.FC<AchievementsSectionProps> = ({ title = 'Recent Achievements', items, style }) => {
  return (
    <View style={[styles.achievementsSection, style]}>
      <Text style={styles.achievementsTitle}>{title}</Text>
      {items.map((a, idx) => (
        <View key={idx} style={styles.achievementItem}>
          <View style={styles.achievementBadge}> 
            <Text style={styles.achievementBadgeEmoji}>{a.emoji}</Text>
          </View>
          <View style={styles.achievementTextWrap}>
            <Text style={styles.achievementName}>{a.name}</Text>
            <Text style={styles.achievementDesc}>{a.desc}</Text>
          </View>
        </View>
      ))}
    </View>
  );
};

// Styles adapted to solid brand colors (no gradients)
const styles = StyleSheet.create({
  // Profile header styles
  profileHeader: {
    backgroundColor: colors.primary,
    paddingHorizontal: 30,
    paddingBottom: 40,
    paddingTop: 10,
  },
  profilePicsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 20,
    marginBottom: 25,
    position: 'relative',
  },
  profilePic: {
    width: 100,
    height: 100,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 25,
    elevation: 8,
    borderWidth: 4,
    borderColor: 'rgba(255,255,255,0.3)',
    overflow: 'hidden',
    backgroundColor: colors.white,
  },
  profilePicSolid: {
    backgroundColor: colors.white,
  },
  profileImage: {
    width: '100%',
    height: '100%',
    borderRadius: 50,
  },
  profileEmoji: { fontSize: 40 },
  connectionHeart: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    marginLeft: -20,
    marginTop: -20,
    backgroundColor: colors.darkerPink,
    borderRadius: 20,
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors.darkerPink,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 15,
    elevation: 10,
    zIndex: 10,
  },
  heartEmoji: { fontSize: 24, color: colors.white },
  coupleNames: {
    fontSize: 28,
    fontWeight: '700',
    color: colors.white,
    marginBottom: 8,
    letterSpacing: -0.5,
    textAlign: 'center',
  },
  relationshipStatus: {
    fontSize: 16,
    fontWeight: '400',
    color: colors.white,
    opacity: 0.9,
    textAlign: 'center',
  },

  // Stats overview styles
  statsOverview: {
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: 25,
    marginBottom: 25,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.08,
    shadowRadius: 30,
    elevation: 8,
  },
  statsTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#2c3e2d',
    marginBottom: 20,
    textAlign: 'center',
  },
  statsGrid: { flexDirection: 'row', gap: 20, marginBottom: 20 },
  statItem: {
    flex: 1,
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#f8f9fa',
    borderRadius: 16,
  },
  statIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  statIconEmoji: { fontSize: 20, color: colors.white },
  statNumber: { fontSize: 24, fontWeight: '700', color: '#2c3e2d', marginBottom: 5 },
  statLabel: { fontSize: 14, color: '#666', fontWeight: '500' },

  // Favorites styles
  favoritesSection: {
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: 25,
    marginBottom: 25,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.08,
    shadowRadius: 30,
    elevation: 8,
  },
  favoritesTitle: { fontSize: 18, fontWeight: '600', color: '#2c3e2d', marginBottom: 15 },
  favoriteItem: { flexDirection: 'row', alignItems: 'center', gap: 12, paddingVertical: 12, borderBottomWidth: 1, borderBottomColor: '#f0f0f0' },
  favoriteItemLast: { borderBottomWidth: 0 },
  favoriteEmoji: { fontSize: 24 },
  favoriteText: { fontSize: 16, color: '#2c3e2d', fontWeight: '500' },

  // Achievements styles
  achievementsSection: {
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: 25,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.08,
    shadowRadius: 30,
    elevation: 8,
  },
  achievementsTitle: { fontSize: 18, fontWeight: '600', color: '#2c3e2d', marginBottom: 15 },
  achievementItem: { flexDirection: 'row', alignItems: 'center', gap: 12, paddingVertical: 12 },
  achievementBadge: { width: 40, height: 40, borderRadius: 20, backgroundColor: colors.secondary, alignItems: 'center', justifyContent: 'center' },
  achievementBadgeEmoji: { fontSize: 16 },
  achievementTextWrap: { flex: 1 },
  achievementName: { fontSize: 16, fontWeight: '600', color: '#2c3e2d', marginBottom: 2 },
  achievementDesc: { fontSize: 14, color: '#666' },
});

export default {
  ProfileHeader,
  StatsOverview,
  FavoritesSection,
  AchievementsSection,
};


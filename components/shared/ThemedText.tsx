/**
 * ThemedText Component
 * 
 * A simple wrapper that applies theme-aware styling to any Text.
 * Use this instead of regular Text components to ensure consistent theming.
 * 
 * <AUTHOR> Us Team
 * @version 1.0.0
 */

import React from 'react';
import { Text, TextProps, TextStyle } from 'react-native';
import { useAppSettings } from '../../hooks/useAppSettings';

interface ThemedTextProps extends TextProps {
  /**
   * Text variant for consistent styling
   */
  variant?: 'title' | 'body' | 'caption' | 'heading' | 'subheading';
  /**
   * Use secondary text color
   */
  secondary?: boolean;
  /**
   * Whether to apply theme colors
   */
  themed?: boolean;
}

/**
 * ThemedText component that automatically applies theme colors and typography
 */
export const ThemedText: React.FC<ThemedTextProps> = ({
  style,
  variant = 'body',
  secondary = false,
  themed = true,
  ...props
}) => {
  const { currentTheme, dynamicStyles } = useAppSettings();

  const getVariantStyle = (): TextStyle => {
    switch (variant) {
      case 'title':
        return dynamicStyles.title;
      case 'body':
        return dynamicStyles.body;
      case 'caption':
        return dynamicStyles.caption;
      case 'heading':
        return {
          fontSize: 24,
          fontWeight: 'bold',
          color: themed ? currentTheme.text : undefined,
        };
      case 'subheading':
        return {
          fontSize: 20,
          fontWeight: '600',
          color: themed ? currentTheme.text : undefined,
        };
      default:
        return dynamicStyles.body;
    }
  };

  const themedStyle: TextStyle = themed
    ? {
        color: secondary ? currentTheme.textSecondary : currentTheme.text,
      }
    : {};

  return (
    <Text style={[getVariantStyle(), themedStyle, style]} {...props} />
  );
};

export default ThemedText;

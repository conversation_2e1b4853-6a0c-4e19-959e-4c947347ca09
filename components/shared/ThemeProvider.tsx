/**
 * Global Theme Provider System
 *
 * Provides comprehensive theme support throughout the entire app.
 * Automatically applies dark/light mode to all components.
 *
 * <AUTHOR> Us Team
 * @version 1.0.0
 */

import React, { createContext, useContext, ReactNode } from 'react';
import { View, ViewStyle, StatusBar } from 'react-native';
import { useAppSettings } from '../../hooks/useAppSettings';

// Theme Context
interface ThemeContextType {
  currentTheme: any;
  isDarkMode: boolean;
  dynamicStyles: any;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Global Theme Provider Component
interface GlobalThemeProviderProps {
  children: ReactNode;
}

export const GlobalThemeProvider: React.FC<GlobalThemeProviderProps> = ({ children }) => {
  const { currentTheme, isDarkMode, dynamicStyles } = useAppSettings();

  const contextValue: ThemeContextType = {
    currentTheme,
    isDarkMode,
    dynamicStyles,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={currentTheme.background}
      />
      <View style={{ flex: 1, backgroundColor: currentTheme.background }}>
        {children}
      </View>
    </ThemeContext.Provider>
  );
};

// Hook to use theme context
export const useGlobalTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useGlobalTheme must be used within a GlobalThemeProvider');
  }
  return context;
};

// Simple hook for backward compatibility
export const useThemeColors = () => {
  const { currentTheme, isDarkMode } = useGlobalTheme();

  return {
    // All theme colors
    ...currentTheme,
    // Convenience flag
    isDarkMode,
  };
};

export default GlobalThemeProvider;

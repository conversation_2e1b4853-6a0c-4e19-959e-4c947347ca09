/**
 * Authentication Components
 * 
 * Specialized components for authentication and onboarding flows.
 * These components consolidate common patterns found in auth screens.
 * 
 * <AUTHOR> Us Team
 * @version 2.0.0
 */

import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Dimensions
} from 'react-native';
import { colors } from '../../utils/colors';

const { width } = Dimensions.get('window');

// ============================================================================
// AUTH LAYOUT COMPONENTS
// ============================================================================

/**
 * Props for the AuthScreenLayout component
 */
interface AuthScreenLayoutProps {
  /** Screen content */
  children: React.ReactNode;
  /** Background color */
  backgroundColor?: string;
  /** Whether to use keyboard avoiding view */
  useKeyboardAvoiding?: boolean;
  /** Whether content is scrollable */
  scrollable?: boolean;
  /** Custom styles */
  style?: any;
}

/**
 * Base layout for authentication screens
 */
export const AuthScreenLayout: React.FC<AuthScreenLayoutProps> = ({ 
  children, 
  backgroundColor = colors.primary,
  useKeyboardAvoiding = true,
  scrollable = true,
  style
}) => {
  const content = scrollable ? (
    <ScrollView 
      contentContainerStyle={styles.scrollContent}
      showsVerticalScrollIndicator={false}
    >
      {children}
    </ScrollView>
  ) : (
    <View style={styles.content}>
      {children}
    </View>
  );

  const wrappedContent = useKeyboardAvoiding ? (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.keyboardAvoidingView}
    >
      {content}
    </KeyboardAvoidingView>
  ) : content;

  return (
    <View style={[styles.container, { backgroundColor }, style]}>
      {wrappedContent}
    </View>
  );
};

/**
 * Props for the AuthHeader component
 */
interface AuthHeaderProps {
  /** Header title */
  title: string;
  /** Header subtitle */
  subtitle?: string;
  /** Logo/icon */
  logo?: React.ReactNode;
  /** Custom styles */
  style?: any;
}

/**
 * Header component for auth screens
 */
export const AuthHeader: React.FC<AuthHeaderProps> = ({ 
  title, 
  subtitle, 
  logo,
  style 
}) => {
  return (
    <View style={[styles.header, style]}>
      {logo && <View style={styles.logoContainer}>{logo}</View>}
      <Text style={styles.title}>{title}</Text>
      {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
    </View>
  );
};

// ============================================================================
// AUTH FORM COMPONENTS
// ============================================================================

/**
 * Props for the AuthInput component
 */
interface AuthInputProps {
  /** Input value */
  value: string;
  /** Text change handler */
  onChangeText: (text: string) => void;
  /** Placeholder text */
  placeholder: string;
  /** Input label */
  label?: string;
  /** Input type */
  type?: 'text' | 'email' | 'password';
  /** Error message */
  error?: string;
  /** Disabled state */
  disabled?: boolean;
  /** Left icon */
  leftIcon?: React.ReactNode;
  /** Right icon */
  rightIcon?: React.ReactNode;
  /** Custom styles */
  style?: any;
}

/**
 * Specialized input component for auth forms
 */
export const AuthInput: React.FC<AuthInputProps> = ({ 
  value, 
  onChangeText, 
  placeholder, 
  label,
  type = 'text',
  error,
  disabled = false,
  leftIcon,
  rightIcon,
  style
}) => {
  return (
    <View style={[styles.inputContainer, style]}>
      {label && <Text style={styles.inputLabel}>{label}</Text>}
      <View style={[
        styles.inputWrapper,
        error && styles.inputWrapperError,
        disabled && styles.inputWrapperDisabled
      ]}>
        {leftIcon && (
          <View style={styles.leftIconContainer}>
            {leftIcon}
          </View>
        )}
        <TextInput
          style={styles.input}
          placeholder={placeholder}
          value={value}
          onChangeText={onChangeText}
          secureTextEntry={type === 'password'}
          keyboardType={type === 'email' ? 'email-address' : 'default'}
          autoCapitalize={type === 'email' ? 'none' : 'sentences'}
          autoCorrect={type === 'email' ? false : true}
          editable={!disabled}
          placeholderTextColor="rgba(255, 255, 255, 0.7)"
        />
        {rightIcon && (
          <View style={styles.rightIconContainer}>
            {rightIcon}
          </View>
        )}
      </View>
      {error && <Text style={styles.inputErrorText}>{error}</Text>}
    </View>
  );
};

/**
 * Props for the AuthButton component
 */
interface AuthButtonProps {
  /** Button title */
  title: string;
  /** Button press handler */
  onPress: () => void;
  /** Button variant */
  variant?: 'primary' | 'secondary' | 'ghost';
  /** Whether button is disabled */
  disabled?: boolean;
  /** Loading state */
  loading?: boolean;
  /** Right icon */
  rightIcon?: React.ReactNode;
  /** Custom styles */
  style?: any;
}

/**
 * Specialized button component for auth screens
 */
export const AuthButton: React.FC<AuthButtonProps> = ({ 
  title, 
  onPress, 
  variant = 'primary',
  disabled = false,
  loading = false,
  rightIcon,
  style
}) => {
  const getButtonStyle = () => {
    switch (variant) {
      case 'secondary':
        return styles.secondaryButton;
      case 'ghost':
        return styles.ghostButton;
      default:
        return styles.primaryButton;
    }
  };

  const getTextStyle = () => {
    switch (variant) {
      case 'secondary':
        return styles.secondaryButtonText;
      case 'ghost':
        return styles.ghostButtonText;
      default:
        return styles.primaryButtonText;
    }
  };

  return (
    <TouchableOpacity
      style={[
        getButtonStyle(),
        disabled && styles.buttonDisabled,
        style
      ]}
      onPress={onPress}
      disabled={disabled || loading}
    >
      <View style={styles.buttonContent}>
        {loading ? (
          <Text style={getTextStyle()}>Loading...</Text>
        ) : (
          <>
            <Text style={getTextStyle()}>{title}</Text>
            {rightIcon && (
              <View style={styles.rightIconContainer}>
                {rightIcon}
              </View>
            )}
          </>
        )}
      </View>
    </TouchableOpacity>
  );
};

// ============================================================================
// FEATURE SHOWCASE COMPONENTS
// ============================================================================

/**
 * Props for the FeatureCard component
 */
interface FeatureCardProps {
  /** Feature icon */
  icon: React.ReactNode;
  /** Feature title */
  title: string;
  /** Feature description */
  description: string;
  /** Accent color */
  color: string;
  /** Custom styles */
  style?: any;
}

/**
 * Feature card component for showcasing app features
 */
export const FeatureCard: React.FC<FeatureCardProps> = ({ 
  icon, 
  title, 
  description, 
  color,
  style
}) => {
  return (
    <View style={[styles.featureCard, { borderLeftColor: color }, style]}>
      <View style={[styles.featureIconContainer, { backgroundColor: `${color}20` }]}>
        {icon}
      </View>
      <View style={styles.featureContent}>
        <Text style={styles.featureTitle}>{title}</Text>
        <Text style={styles.featureDescription}>{description}</Text>
      </View>
    </View>
  );
};

/**
 * Props for the FeatureShowcase component
 */
interface FeatureShowcaseProps {
  /** Section title */
  title: string;
  /** Feature cards */
  features: Array<{
    icon: React.ReactNode;
    title: string;
    description: string;
    color: string;
  }>;
  /** Custom styles */
  style?: any;
}

/**
 * Feature showcase section component
 */
export const FeatureShowcase: React.FC<FeatureShowcaseProps> = ({ 
  title, 
  features, 
  style 
}) => {
  return (
    <View style={[styles.featureShowcase, style]}>
      <Text style={styles.featureShowcaseTitle}>{title}</Text>
      {features.map((feature, index) => (
        <FeatureCard
          key={index}
          icon={feature.icon}
          title={feature.title}
          description={feature.description}
          color={feature.color}
        />
      ))}
    </View>
  );
};

// ============================================================================
// ONBOARDING COMPONENTS
// ============================================================================

/**
 * Props for the OnboardingStep component
 */
interface OnboardingStepProps {
  /** Step title */
  title: string;
  /** Step description */
  description: string;
  /** Step icon */
  icon: React.ReactNode;
  /** Background color */
  backgroundColor?: string;
  /** Custom styles */
  style?: any;
}

/**
 * Individual onboarding step component
 */
export const OnboardingStep: React.FC<OnboardingStepProps> = ({ 
  title, 
  description, 
  icon, 
  backgroundColor = colors.primary,
  style
}) => {
  return (
    <View style={[styles.onboardingStep, { backgroundColor }, style]}>
      <View style={styles.onboardingContent}>
        <View style={styles.onboardingIconContainer}>
          {icon}
        </View>
        <Text style={styles.onboardingTitle}>{title}</Text>
        <Text style={styles.onboardingDescription}>{description}</Text>
      </View>
    </View>
  );
};

/**
 * Props for the OnboardingNavigation component
 */
interface OnboardingNavigationProps {
  /** Current step */
  currentStep: number;
  /** Total steps */
  totalSteps: number;
  /** Next button handler */
  onNext: () => void;
  /** Skip button handler */
  onSkip: () => void;
  /** Complete button handler */
  onComplete: () => void;
  /** Whether to show skip button */
  showSkip?: boolean;
  /** Custom styles */
  style?: any;
  /** Disable primary action */
  disabled?: boolean;
}

/**
 * Navigation component for onboarding flow
 */
export const OnboardingNavigation: React.FC<OnboardingNavigationProps> = ({
  currentStep,
  totalSteps,
  onNext,
  onSkip,
  onComplete,
  showSkip = true,
  style,
  disabled = false
}) => {
  const isLastStep = currentStep === totalSteps - 1;

  return (
    <View style={[styles.onboardingNavigation, style]}>
      {/* Progress indicator */}
      <View style={styles.progressContainer}>
        {Array.from({ length: totalSteps }, (_, index) => (
          <View
            key={index}
            style={[
              styles.progressDot,
              index <= currentStep && styles.progressDotActive
            ]}
          />
        ))}
      </View>

      {/* Action buttons */}
      <View style={styles.navigationButtons}>
        {showSkip && !isLastStep && (
          <AuthButton
            title="Skip"
            onPress={onSkip}
            variant="ghost"
            style={styles.skipButton}
          />
        )}
        
        <AuthButton
          title={isLastStep ? "Get Started" : "Next"}
          onPress={isLastStep ? onComplete : onNext}
          variant="primary"
          disabled={disabled}
          style={styles.nextButton}
        />
      </View>
    </View>
  );
};

// ============================================================================
// STYLES
// ============================================================================

const styles = StyleSheet.create({
  // Auth Layout Styles
  container: {
    flex: 1,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 40,
  },
  header: {
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 40,
    paddingHorizontal: 20,
  },
  logoContainer: {
    marginBottom: 16,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: colors.textInverse,
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: colors.whiteOverlayStrong,
    textAlign: 'center',
    lineHeight: 22,
  },

  // Input Styles
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textInverse,
    marginBottom: 8,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.whiteOverlayLight,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.whiteOverlayMedium,
    paddingHorizontal: 16,
    minHeight: 56,
  },
  inputWrapperError: {
    borderColor: colors.error,
  },
  inputWrapperDisabled: {
    opacity: 0.6,
  },
  leftIconContainer: {
    marginRight: 12,
  },
  rightIconContainer: {
    marginLeft: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: colors.textInverse,
    paddingVertical: 16,
  },
  inputErrorText: {
    fontSize: 12,
    color: colors.error,
    marginTop: 4,
  },

  // Button Styles
  primaryButton: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  secondaryButton: {
    backgroundColor: colors.whiteOverlayMedium,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: colors.whiteOverlayStrong,
  },
  ghostButton: {
    backgroundColor: 'transparent',
    borderRadius: 12,
    padding: 16,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.primary,
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textInverse,
  },
  ghostButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.whiteOverlayStrong,
  },

  // Feature Card Styles
  featureCard: {
    flexDirection: 'row',
    backgroundColor: colors.whiteOverlayStronger,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderLeftWidth: 4,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  featureIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 20,
  },
  featureShowcase: {
    paddingHorizontal: 20,
    marginBottom: 40,
  },
  featureShowcaseTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.textInverse,
    textAlign: 'center',
    marginBottom: 24,
  },

  // Onboarding Styles
  onboardingStep: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  onboardingContent: {
    alignItems: 'center',
  },
  onboardingIconContainer: {
    marginBottom: 32,
  },
  onboardingTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: colors.textInverse,
    textAlign: 'center',
    marginBottom: 16,
  },
  onboardingDescription: {
    fontSize: 18,
    color: colors.whiteOverlayStrong,
    textAlign: 'center',
    lineHeight: 26,
  },
  onboardingNavigation: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 20,
    paddingBottom: 40,
    paddingTop: 20,
    backgroundColor: colors.whiteOverlayLight,
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 20,
  },
  progressDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.whiteOverlayStrong,
    marginHorizontal: 4,
  },
  progressDotActive: {
    backgroundColor: colors.white,
  },
  navigationButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  skipButton: {
    flex: 1,
    marginRight: 12,
  },
  nextButton: {
    flex: 2,
  },
});

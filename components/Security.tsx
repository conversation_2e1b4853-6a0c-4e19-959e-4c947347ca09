import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';

import { Shield, Lock, Eye, Database, Users, ArrowLeft, CheckCircle, AlertTriangle } from 'lucide-react-native';
import { colors } from '../utils/colors';

interface SecurityProps {
  onBack: () => void;
}

export default function Security({ onBack }: SecurityProps) {
  return (
    <View style={styles.container}>
      <View
        style={[styles.header, { backgroundColor: colors.primary }]}
      >
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <ArrowLeft size={24} color={colors.white} />
        </TouchableOpacity>
        <Shield size={32} color={colors.white} />
        <Text style={styles.headerTitle}>Security & Privacy</Text>
        <Text style={styles.headerSubtitle}>Your data is protected with enterprise-grade security</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <CheckCircle size={20} color={colors.success} />
            <Text style={styles.sectionTitle}>Security Features</Text>
          </View>
          <Text style={styles.sectionText}>
            We implement industry-leading security measures to protect your relationship data and ensure your privacy.
          </Text>
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Lock size={20} color={colors.lightPink} />
            <Text style={styles.sectionTitle}>Data Encryption</Text>
          </View>
          <Text style={styles.sectionText}>
            • All data is encrypted using AES-256 encryption{'\n'}
            • Secure local storage with Expo SecureStore{'\n'}
            • No data transmission to external servers{'\n'}
            • End-to-end encryption for all sensitive information
          </Text>
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Database size={20} color={colors.lightPink} />
            <Text style={styles.sectionTitle}>Secure Storage</Text>
          </View>
          <Text style={styles.sectionText}>
            • Data stored locally on your device only{'\n'}
            • Encrypted storage with secure access controls{'\n'}
            • Automatic data backup and recovery{'\n'}
            • No cloud storage or external data centers
          </Text>
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Shield size={20} color={colors.lightPink} />
            <Text style={styles.sectionTitle}>Privacy Protection</Text>
          </View>
          <Text style={styles.sectionText}>
            • Zero data collection beyond what you provide{'\n'}
            • No analytics or tracking of your activities{'\n'}
            • No sharing with third parties or advertisers{'\n'}
            • Complete control over your data
          </Text>
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Eye size={20} color={colors.lightPink} />
            <Text style={styles.sectionTitle}>Access Control</Text>
          </View>
          <Text style={styles.sectionText}>
            • Data accessible only to you and your partner{'\n'}
            • Secure authentication and session management{'\n'}
            • Automatic logout for security{'\n'}
            • No unauthorized access possible
          </Text>
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <AlertTriangle size={20} color={colors.warning} />
            <Text style={styles.sectionTitle}>Security Best Practices</Text>
          </View>
          <Text style={styles.sectionText}>
            • Keep your device updated{'\n'}
            • Use strong device passwords{'\n'}
            • Enable device encryption{'\n'}
            • Be cautious with device sharing
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Compliance & Standards</Text>
          <Text style={styles.sectionText}>
            • GDPR compliant privacy practices{'\n'}
            • Industry-standard security protocols{'\n'}
            • Regular security audits and updates{'\n'}
            • Transparent security practices
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Your Security Rights</Text>
          <Text style={styles.sectionText}>
            • Complete data ownership and control{'\n'}
            • Right to delete all data at any time{'\n'}
            • Data export for backup purposes{'\n'}
            • Full transparency about security measures
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Security Contact</Text>
          <Text style={styles.sectionText}>
            If you have security concerns or questions about our security practices, 
            please contact us through the app settings. We take all security matters seriously.
          </Text>
        </View>

        <View style={styles.footer}>
          <Text style={styles.footerText}>
            Security measures last updated: {new Date().toLocaleDateString()}
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FEF3E2',
  },
  header: {
    paddingTop: 60,
    paddingBottom: 24,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: 20,
    padding: 8,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#FFFFFF',
    marginTop: 16,
    marginBottom: 4,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.9,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginLeft: 8,
  },
  sectionText: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 20,
    marginBottom: 40,
  },
  footerText: {
    fontSize: 12,
    color: '#9CA3AF',
    fontStyle: 'italic',
  },
});

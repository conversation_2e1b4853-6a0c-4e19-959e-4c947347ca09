import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, TextInput, ViewStyle } from 'react-native';
import { colors } from '../../utils/colors';
import ds, { brand, spacing as S, radii as R, shadows as SH, getGlassStyle, Tone, toneToColor } from '../../utils/designSystem';

// 1) Header / Navigation patterns
export interface HeaderBarProps {
  title?: string;
  left?: React.ReactNode;
  right?: React.ReactNode;
  tone?: Tone; // controls header background color
}
export const HeaderBar: React.FC<HeaderBarProps> = ({ title, left, right, tone = 'sage' }) => {
  const bg = toneToColor(tone, 'solid');
  return (
    <View style={[styles.headerBar, { backgroundColor: bg }]}>
      <View style={styles.headerSide}>{left}</View>
      <Text style={styles.headerTitle}>{title}</Text>
      <View style={styles.headerSide}>{right}</View>
    </View>
  );
};

// 2) Card layouts (stat/content/feature)
export interface CardProps { children: React.ReactNode; style?: ViewStyle; padded?: boolean }
export const Card: React.FC<CardProps> = ({ children, style, padded = true }) => (
  <View style={[styles.card, style, padded && styles.cardPadded]}>{children}</View>
);

export interface StatTileProps { emoji: string; number: number | string; label: string; tone?: Tone }
export const StatTile: React.FC<StatTileProps> = ({ emoji, number, label, tone = 'sage' }) => (
  <View style={styles.statTile}>
    <View style={[styles.statIcon, { backgroundColor: toneToColor(tone, 'solid') }]}>
      <Text style={styles.statIconEmoji}>{emoji}</Text>
    </View>
    <Text style={styles.statNumber}>{number}</Text>
    <Text style={styles.statLabel}>{label}</Text>
  </View>
);

// 3) Button variants
export interface ButtonProps { title: string; onPress: () => void; variant?: 'primary'|'secondary'|'pill'|'outline'; tone?: Tone; disabled?: boolean; style?: ViewStyle }
export const Button: React.FC<ButtonProps> = ({ title, onPress, variant='primary', tone='sage', disabled, style }) => {
  const base = [styles.btnBase];
  const bg = toneToColor(tone, 'solid');
  let textColor = colors.white;

  if (variant === 'pill') base.push(styles.btnPill);
  if (variant === 'secondary') {
    base.push({ backgroundColor: colors.white, borderWidth: 1, borderColor: 'rgba(0,0,0,0.08)' });
    textColor = colors.textPrimary;
  }
  if (variant === 'outline') {
    base.push({ backgroundColor: 'transparent', borderWidth: 1, borderColor: bg });
    textColor = bg;
  }
  if (variant === 'primary') base.push({ backgroundColor: bg });

  return (
    <TouchableOpacity onPress={onPress} disabled={disabled} style={[...base, style, disabled && { opacity: 0.6 }]}>
      <Text style={[styles.btnText, { color: textColor }]}>{title}</Text>
    </TouchableOpacity>
  );
};

// 4) Form components
export interface InputProps { value: string; onChangeText: (v:string)=>void; placeholder?: string; style?: ViewStyle }
export const Input: React.FC<InputProps> = ({ value, onChangeText, placeholder, style }) => (
  <TextInput
    value={value}
    onChangeText={onChangeText}
    placeholder={placeholder}
    placeholderTextColor={colors.textTertiary}
    style={[styles.input, style]}
  />
);

export interface GlassCardProps { children: React.ReactNode; style?: ViewStyle }
export const GlassCard: React.FC<GlassCardProps> = ({ children, style }) => (
  <View style={[styles.glassCard, style]}>{children}</View>
);

// 5) Profile / Avatar displays
export interface AvatarProps { size?: number; emoji?: string; badge?: string; }
export const Avatar: React.FC<AvatarProps> = ({ size=40, emoji='😊', badge }) => (
  <View style={[styles.avatar, { width: size, height: size, borderRadius: size/2 }]}>
    <Text style={{ fontSize: size * 0.5 }}>{emoji}</Text>
    {badge ? (
      <View style={[styles.avatarBadge, { right: -size*0.05, bottom: -size*0.05 }]}>
        <Text style={styles.avatarBadgeText}>{badge}</Text>
      </View>
    ) : null}
  </View>
);

// 6) List / Grid layouts
export const ListItem: React.FC<{ left?: React.ReactNode; title: string; subtitle?: string; right?: React.ReactNode }>
  = ({ left, title, subtitle, right }) => (
  <View style={styles.listItem}>
    <View style={styles.listLeft}>{left}</View>
    <View style={styles.listBody}>
      <Text style={styles.listTitle}>{title}</Text>
      {subtitle ? <Text style={styles.listSubtitle}>{subtitle}</Text> : null}
    </View>
    <View style={styles.listRight}>{right}</View>
  </View>
);

// 7) Badge / Tag components
export const Badge: React.FC<{ label: string; tone?: Tone; soft?: boolean }>
  = ({ label, tone='sage', soft=true }) => (
  <View style={[styles.badge, soft ? { backgroundColor: `${toneToColor(tone) }33` } : { backgroundColor: toneToColor(tone) }]}>
    <Text style={[styles.badgeText, soft ? { color: brand.text } : { color: colors.white }]}>{label}</Text>
  </View>
);

// 8) Modal / Overlay patterns
export const OverlayCard: React.FC<{ children: React.ReactNode; style?: ViewStyle }>
  = ({ children, style }) => (
  <View style={[styles.overlayCard, style]}>{children}</View>
);

// 9) Section header
export const SectionHeader: React.FC<{ title: string; action?: React.ReactNode }>
  = ({ title, action }) => (
  <View style={styles.sectionHeader}>
    <Text style={styles.sectionTitle}>{title}</Text>
    {action}
  </View>
);

// 10) Pill button group
export const PillGroup: React.FC<{ options: string[]; selected: string; onSelect: (v:string)=>void }>
  = ({ options, selected, onSelect }) => (
  <View style={styles.pillGroup}>
    {options.map(opt => (
      <TouchableOpacity key={opt} onPress={() => onSelect(opt)} style={[styles.pillItem, selected === opt && styles.pillItemActive]}>
        <Text style={[styles.pillText, selected === opt && styles.pillTextActive]}>{opt}</Text>
      </TouchableOpacity>
    ))}
  </View>
);

const styles = StyleSheet.create({
  headerBar: {
    height: 56, borderBottomWidth: 0, alignItems: 'center', flexDirection: 'row', paddingHorizontal: S.md,
  },
  headerSide: { width: 56, alignItems: 'center', justifyContent: 'center' },
  headerTitle: { flex: 1, textAlign: 'center', color: colors.white, fontSize: 18, fontWeight: '700' },

  card: {
    backgroundColor: colors.white, borderRadius: R.lg, ...SH.md,
  },
  cardPadded: { padding: S.lg },

  statTile: { flex: 1, alignItems: 'center', backgroundColor: '#f8f9fa', borderRadius: R.lg, padding: S.md },
  statIcon: { width: 50, height: 50, borderRadius: 25, alignItems: 'center', justifyContent: 'center', marginBottom: S.sm },
  statIconEmoji: { fontSize: 20, color: colors.white },
  statNumber: { fontSize: 24, fontWeight: '700', color: colors.textPrimary, marginBottom: 4 },
  statLabel: { fontSize: 14, color: colors.textSecondary, fontWeight: '500' },

  btnBase: { paddingVertical: 12, paddingHorizontal: 20, borderRadius: R.md, alignItems: 'center', justifyContent: 'center' },
  btnPill: { borderRadius: R.pill },
  btnText: { color: colors.white, fontWeight: '700' },

  input: { backgroundColor: colors.white, borderRadius: R.md, paddingHorizontal: S.md, paddingVertical: 12, borderWidth: 1, borderColor: 'rgba(0,0,0,0.1)' },
  glassCard: { borderRadius: R.xl, padding: S.md, ...getGlassStyle(0.8), ...SH.md },

  avatar: { backgroundColor: colors.white, borderWidth: 1, borderColor: 'rgba(0,0,0,0.08)', alignItems: 'center', justifyContent: 'center' },
  avatarBadge: { position: 'absolute', backgroundColor: colors.secondary, borderRadius: 12, paddingHorizontal: 6, paddingVertical: 2 },
  avatarBadgeText: { color: colors.white, fontSize: 10, fontWeight: '800' },

  listItem: { flexDirection: 'row', alignItems: 'center', paddingVertical: S.md },
  listLeft: { width: 40, alignItems: 'center' },
  listBody: { flex: 1 },
  listRight: { width: 40, alignItems: 'center' },
  listTitle: { fontSize: 16, fontWeight: '600', color: colors.textPrimary },
  listSubtitle: { fontSize: 12, color: colors.textSecondary },

  badge: { borderRadius: R.pill, paddingHorizontal: 8, paddingVertical: 4, alignSelf: 'flex-start' },
  badgeText: { fontSize: 11, fontWeight: '800' },

  overlayCard: { borderRadius: R.xl, padding: S.lg, backgroundColor: colors.white, ...SH.lg },

  sectionHeader: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginBottom: S.sm },
  sectionTitle: { fontSize: 18, fontWeight: '700', color: colors.textPrimary },

  pillGroup: { flexDirection: 'row', backgroundColor: '#f3f4f6', borderRadius: R.pill, padding: 4 },
  pillItem: { paddingHorizontal: 12, paddingVertical: 8, borderRadius: R.pill },
  pillItemActive: { backgroundColor: colors.white },
  pillText: { fontSize: 12, color: colors.textSecondary, fontWeight: '700' },
  pillTextActive: { color: colors.textPrimary },
});

export default {
  HeaderBar,
  Card,
  StatTile,
  Button,
  Input,
  GlassCard,
  Avatar,
  ListItem,
  Badge,
  OverlayCard,
  SectionHeader,
  PillGroup,
};


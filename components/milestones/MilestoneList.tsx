import React from 'react';
import { View, Text, TouchableOpacity, FlatList } from 'react-native';
import { CoupleMilestone, MilestoneTemplate } from '../../services/milestoneService';
import { colors } from '../../utils/colors';

interface MilestoneListProps {
  milestones: CoupleMilestone[];
  templates: MilestoneTemplate[];
  onMilestonePress: (milestone: CoupleMilestone, template: MilestoneTemplate) => void;
  groupByCategory?: boolean;
  showProgress?: boolean;
}

export const MilestoneList: React.FC<MilestoneListProps> = ({
  milestones,
  templates,
  onMilestonePress,
  groupByCategory = true,
  showProgress = true,
}) => {
  const getMilestoneTemplate = (milestone: CoupleMilestone): MilestoneTemplate | undefined => {
    return milestone.template || templates.find(t => t.id === milestone.milestone_template_id);
  };

  const renderMilestoneItem = ({ item: milestone }: { item: CoupleMilestone }) => {
    const template = getMilestoneTemplate(milestone);
    if (!template) return null;

    const completionPercentage = milestone.is_completed ? 100 : 
      (Object.keys(milestone.milestone_data || {}).length / (template.field_schema?.fields?.length || 1)) * 100;

    return (
      <TouchableOpacity
        style={[
          styles.milestoneItem,
          milestone.is_completed && styles.completedMilestone,
        ]}
        onPress={() => onMilestonePress(milestone, template)}
      >
        <View style={styles.milestoneHeader}>
          <View style={styles.milestoneIcon}>
            <Text style={styles.iconText}>{template.ui_config?.icon || '📝'}</Text>
          </View>
          
          <View style={styles.milestoneContent}>
            <Text style={[
              styles.milestoneTitle,
              milestone.is_completed && styles.completedText,
            ]}>
              {template.title}
            </Text>
            
            <Text style={styles.milestoneDescription} numberOfLines={2}>
              {template.description}
            </Text>
            
            {milestone.completion_date && (
              <Text style={styles.completionDate}>
                Completed: {new Date(milestone.completion_date).toLocaleDateString()}
              </Text>
            )}
          </View>
          
          <View style={styles.milestoneStatus}>
            {milestone.is_completed ? (
              <View style={styles.completedBadge}>
                <Text style={styles.completedBadgeText}>✓</Text>
              </View>
            ) : (
              <View style={styles.progressContainer}>
                <Text style={styles.progressText}>{Math.round(completionPercentage)}%</Text>
                <View style={styles.progressBar}>
                  <View 
                    style={[
                      styles.progressFill,
                      { width: `${completionPercentage}%` }
                    ]} 
                  />
                </View>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderCategorySection = (category: string, categoryMilestones: CoupleMilestone[]) => {
    const completedCount = categoryMilestones.filter(m => m.is_completed).length;
    const totalCount = categoryMilestones.length;
    const categoryProgress = totalCount > 0 ? (completedCount / totalCount) * 100 : 0;

    return (
      <View key={category} style={styles.categorySection}>
        <View style={styles.categoryHeader}>
          <Text style={styles.categoryTitle}>
            {category.replace('_', ' ').toUpperCase()}
          </Text>
          {showProgress && (
            <View style={styles.categoryProgress}>
              <Text style={styles.categoryProgressText}>
                {completedCount}/{totalCount} ({Math.round(categoryProgress)}%)
              </Text>
              <View style={styles.categoryProgressBar}>
                <View 
                  style={[
                    styles.categoryProgressFill,
                    { width: `${categoryProgress}%` }
                  ]} 
                />
              </View>
            </View>
          )}
        </View>
        
        <FlatList
          data={categoryMilestones}
          renderItem={renderMilestoneItem}
          keyExtractor={(item) => item.id}
          scrollEnabled={false}
        />
      </View>
    );
  };

  if (groupByCategory) {
    // Group milestones by category
    const milestonesByCategory = milestones.reduce((acc, milestone) => {
      const template = getMilestoneTemplate(milestone);
      const category = template?.category || 'uncategorized';
      
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(milestone);
      return acc;
    }, {} as Record<string, CoupleMilestone[]>);

    // Sort categories by a predefined order
    const categoryOrder = ['foundation', 'getting_to_know', 'commitment', 'engagement', 'modern'];
    const sortedCategories = Object.keys(milestonesByCategory).sort((a, b) => {
      const aIndex = categoryOrder.indexOf(a);
      const bIndex = categoryOrder.indexOf(b);
      if (aIndex === -1 && bIndex === -1) return a.localeCompare(b);
      if (aIndex === -1) return 1;
      if (bIndex === -1) return -1;
      return aIndex - bIndex;
    });

    return (
      <View style={styles.container}>
        {sortedCategories.map(category => 
          renderCategorySection(category, milestonesByCategory[category])
        )}
      </View>
    );
  }

  // Simple flat list without categories
  return (
    <FlatList
      data={milestones}
      renderItem={renderMilestoneItem}
      keyExtractor={(item) => item.id}
      style={styles.container}
      showsVerticalScrollIndicator={false}
    />
  );
};

const styles = {
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  categorySection: {
    marginBottom: 24,
  },
  categoryHeader: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: 'bold' as const,
    color: colors.text,
    marginBottom: 8,
  },
  categoryProgress: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 12,
  },
  categoryProgressText: {
    fontSize: 14,
    color: colors.textSecondary,
    minWidth: 80,
  },
  categoryProgressBar: {
    flex: 1,
    height: 6,
    backgroundColor: colors.border,
    borderRadius: 3,
  },
  categoryProgressFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 3,
  },
  milestoneItem: {
    backgroundColor: colors.white,
    marginHorizontal: 20,
    marginVertical: 6,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  completedMilestone: {
    backgroundColor: '#F0F9FF',
    borderLeftWidth: 4,
    borderLeftColor: colors.success,
  },
  milestoneHeader: {
    flexDirection: 'row' as const,
    alignItems: 'flex-start' as const,
  },
  milestoneIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.background,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    marginRight: 12,
  },
  iconText: {
    fontSize: 20,
  },
  milestoneContent: {
    flex: 1,
    marginRight: 12,
  },
  milestoneTitle: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: colors.text,
    marginBottom: 4,
  },
  completedText: {
    color: colors.success,
  },
  milestoneDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 20,
    marginBottom: 8,
  },
  completionDate: {
    fontSize: 12,
    color: colors.success,
    fontWeight: '500' as const,
  },
  milestoneStatus: {
    alignItems: 'center' as const,
  },
  completedBadge: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.success,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
  },
  completedBadgeText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: 'bold' as const,
  },
  progressContainer: {
    alignItems: 'center' as const,
    minWidth: 50,
  },
  progressText: {
    fontSize: 12,
    color: colors.textSecondary,
    marginBottom: 4,
  },
  progressBar: {
    width: 40,
    height: 4,
    backgroundColor: colors.border,
    borderRadius: 2,
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 2,
  },
};

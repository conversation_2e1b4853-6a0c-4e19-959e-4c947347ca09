import { logger } from '../utils/logger';
import { Alert, Share, Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from '../lib/supabase/client';

export interface SimpleErrorReport {
  id: string;
  timestamp: string;
  error: string;
  stack?: string;
  component?: string;
  action?: string;
  sessionId?: string; // Anonymous session identifier instead of userId
  metadata?: Record<string, any>; // Will be sanitized to remove PII
  severity?: 'low' | 'medium' | 'high' | 'critical';
  userAgent?: string;
  url?: string;
}

export interface DatabaseErrorReport {
  id: string;
  user_id?: string;
  session_id: string;
  error_message: string;
  stack_trace?: string;
  component?: string;
  action?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  metadata?: Record<string, any>;
  user_agent?: string;
  url?: string;
  created_at: string;
}

class SimpleErrorService {
  private errorQueue: SimpleErrorReport[] = [];
  private readonly STORAGE_KEY = 'simple_error_reports';
  private readonly CONSENT_STORAGE_KEY = 'simple_error_reporting_consent';
  private sessionId: string;
  private hasUserConsent: boolean = false;
  private enableDatabaseLogging: boolean = true;

  // Safe storage access helper
  private async safeStorageGetItem(key: string): Promise<string | null> {
    try {
      if (Platform.OS === 'web') {
        if (typeof window !== 'undefined' && window.localStorage) {
          return window.localStorage.getItem(key);
        }
        return null;
      } else {
        return await AsyncStorage.getItem(key);
      }
    } catch (error) {
      return null;
    }
  }

  private async safeStorageSetItem(key: string, value: string): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        if (typeof window !== 'undefined' && window.localStorage) {
          window.localStorage.setItem(key, value);
        }
      } else {
        await AsyncStorage.setItem(key, value);
      }
    } catch (error) {
      // Silently handle storage errors
    }
  }

  private async safeStorageRemoveItem(key: string): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        if (typeof window !== 'undefined' && window.localStorage) {
          window.localStorage.removeItem(key);
        }
      } else {
        await AsyncStorage.removeItem(key);
      }
    } catch (error) {
      // Silently handle storage errors
    }
  }

  constructor() {
    this.sessionId = this.generateSessionId();
    this.loadErrorQueue();
    this.loadConsentStatus();
  }

  /**
   * Report an error with context (only if user has consented)
   */
  async reportError(
    error: Error | string,
    context: {
      component?: string;
      action?: string;
      metadata?: Record<string, any>;
      severity?: 'low' | 'medium' | 'high' | 'critical';
      userId?: string;
    } = {}
  ): Promise<void> {
    try {
      // Check if user has consented to error reporting
      if (!this.hasUserConsent) {
        logger.debug('Simple error reporting skipped - user has not consented');
        return;
      }

      const severity = context.severity || this.determineSeverity(error, context);
      const errorReport: SimpleErrorReport = {
        id: this.generateId(),
        timestamp: new Date().toISOString(),
        error: this.sanitizeErrorMessage(typeof error === 'string' ? error : error.message),
        stack: typeof error === 'string' ? undefined : this.sanitizeStackTrace(error.stack),
        component: context.component,
        action: context.action,
        sessionId: this.sessionId,
        metadata: this.sanitizeMetadata(context.metadata),
        severity,
        userAgent: this.getUserAgent(),
        url: this.getCurrentUrl(),
      };

      // Add to queue
      this.errorQueue.push(errorReport);

      // Keep only last 20 errors
      if (this.errorQueue.length > 20) {
        this.errorQueue = this.errorQueue.slice(-20);
      }

      // Save to storage
      await this.saveErrorQueue();

      // Log to database if enabled
      if (this.enableDatabaseLogging) {
        await this.logErrorToDatabase(errorReport, context.userId);
      }

      // Log locally
      logger.error('Error reported:', errorReport);

      // Show error to user if it's a critical error
      if (severity === 'critical' || context.action === 'CriticalError') {
        this.showErrorToUser(errorReport);
      }

    } catch (err) {
      logger.error('Failed to report error:', err);
    }
  }

  /**
   * Request user consent for error reporting
   */
  async requestErrorReportingConsent(): Promise<boolean> {
    try {
      await this.safeStorageSetItem(this.CONSENT_STORAGE_KEY, JSON.stringify({ hasConsented: true }));
      this.hasUserConsent = true;
      logger.info('User consented to simple error reporting');
      return true;
    } catch (error) {
      logger.error('Failed to save simple error reporting consent:', error);
      return false;
    }
  }

  /**
   * Revoke user consent for error reporting
   */
  async revokeErrorReportingConsent(): Promise<void> {
    try {
      await this.safeStorageSetItem(this.CONSENT_STORAGE_KEY, JSON.stringify({ hasConsented: false }));
      this.hasUserConsent = false;

      // Clear existing error queue
      this.errorQueue = [];
      await this.saveErrorQueue();

      logger.info('User revoked consent for simple error reporting');
    } catch (error) {
      logger.error('Failed to revoke simple error reporting consent:', error);
    }
  }

  /**
   * Generate anonymous session ID
   */
  private generateSessionId(): string {
    return `simple_session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Sanitize error message to remove potential PII
   */
  private sanitizeErrorMessage(message: string): string {
    if (!message) return '';

    // Remove email addresses
    let sanitized = message.replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[EMAIL_REDACTED]');

    // Remove phone numbers (basic patterns)
    sanitized = sanitized.replace(/\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g, '[PHONE_REDACTED]');

    // Remove potential user IDs (UUIDs and similar patterns)
    sanitized = sanitized.replace(/\b[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\b/gi, '[ID_REDACTED]');

    // Remove file paths that might contain usernames
    sanitized = sanitized.replace(/\/Users\/<USER>\/\s]+/g, '/Users/<USER>');
    sanitized = sanitized.replace(/C:\\Users\\<USER>\\s]+/g, 'C:\\Users\\<USER>\n${errorReport.stack}` : ''}

${errorReport.metadata ? `Additional Info:\n${JSON.stringify(errorReport.metadata, null, 2)}` : ''}

Please send this to: <EMAIL>
      `.trim();

      await Share.share({
        message: errorText,
        title: 'Error Report - Everlasting Us',
      });
    } catch (error) {
      logger.error('Failed to share error report:', error);
    }
  }

  /**
   * Get all error reports as text
   */
  async getAllErrorsAsText(): Promise<string> {
    const errors = this.errorQueue.map(error => `
=== Error Report ===
ID: ${error.id}
Timestamp: ${error.timestamp}
Component: ${error.component || 'Unknown'}
Action: ${error.action || 'Unknown'}
Session ID: ${error.sessionId || 'Anonymous'}

Error: ${error.error}

${error.stack ? `Stack:\n${error.stack}` : ''}

${error.metadata ? `Metadata:\n${JSON.stringify(error.metadata, null, 2)}` : ''}
    `).join('\n');

    return `Everlasting Us - Error Reports\nGenerated: ${new Date().toISOString()}\n\n${errors}`;
  }

  /**
   * Share all error reports
   */
  async shareAllErrors(): Promise<void> {
    try {
      const errorText = await this.getAllErrorsAsText();
      
      await Share.share({
        message: errorText,
        title: 'All Error Reports - Everlasting Us',
      });
    } catch (error) {
      logger.error('Failed to share all errors:', error);
    }
  }

  /**
   * Get error statistics
   */
  getErrorStats(): {
    totalErrors: number;
    recentErrors: SimpleErrorReport[];
    errorTypes: Record<string, number>;
  } {
    const recentErrors = this.errorQueue.slice(-5);
    const errorTypes: Record<string, number> = {};

    this.errorQueue.forEach(error => {
      const type = error.component || 'Unknown';
      errorTypes[type] = (errorTypes[type] || 0) + 1;
    });

    return {
      totalErrors: this.errorQueue.length,
      recentErrors,
      errorTypes,
    };
  }

  /**
   * Clear all error reports
   */
  async clearErrorReports(): Promise<void> {
    this.errorQueue = [];
    await this.safeStorageRemoveItem(this.STORAGE_KEY);
    logger.info('All error reports cleared');
  }

  /**
   * Load error queue from storage
   */
  private async loadErrorQueue(): Promise<void> {
    try {
      const stored = await this.safeStorageGetItem(this.STORAGE_KEY);
      if (stored) {
        this.errorQueue = JSON.parse(stored);
      }
    } catch (error) {
      // Silently handle initialization errors to prevent app crashes
      this.errorQueue = [];
    }
  }

  /**
   * Save error queue to storage
   */
  private async saveErrorQueue(): Promise<void> {
    try {
      await this.safeStorageSetItem(this.STORAGE_KEY, JSON.stringify(this.errorQueue));
    } catch (error) {
      logger.error('Failed to save error queue:', error);
    }
  }

  /**
   * Log error to database
   */
  private async logErrorToDatabase(errorReport: SimpleErrorReport, userId?: string): Promise<void> {
    try {
      const databaseReport: Omit<DatabaseErrorReport, 'created_at'> = {
        id: errorReport.id,
        user_id: userId,
        session_id: errorReport.sessionId || 'unknown',
        error_message: errorReport.error,
        stack_trace: errorReport.stack,
        component: errorReport.component,
        action: errorReport.action,
        severity: errorReport.severity || 'medium',
        metadata: errorReport.metadata,
        user_agent: errorReport.userAgent,
        url: errorReport.url,
      };

      const { error } = await supabase
        .from('error_logs')
        .insert([databaseReport]);

      if (error) {
        logger.error('Failed to log error to database:', error);
      } else {
        logger.debug('Error logged to database successfully');
      }
    } catch (err) {
      logger.error('Database error logging failed:', err);
    }
  }

  /**
   * Determine error severity based on error content and context
   */
  private determineSeverity(error: Error | string, context: any): 'low' | 'medium' | 'high' | 'critical' {
    const errorMessage = typeof error === 'string' ? error : error.message;
    const lowerMessage = errorMessage.toLowerCase();

    // Critical errors
    if (lowerMessage.includes('network error') ||
        lowerMessage.includes('connection failed') ||
        lowerMessage.includes('authentication failed') ||
        context.action === 'CriticalError') {
      return 'critical';
    }

    // High severity errors
    if (lowerMessage.includes('undefined') ||
        lowerMessage.includes('null') ||
        lowerMessage.includes('cannot read property') ||
        lowerMessage.includes('is not a function')) {
      return 'high';
    }

    // Medium severity errors
    if (lowerMessage.includes('warning') ||
        lowerMessage.includes('deprecated')) {
      return 'medium';
    }

    // Default to medium
    return 'medium';
  }

  /**
   * Get user agent string
   */
  private getUserAgent(): string {
    if (Platform.OS === 'web') {
      return typeof navigator !== 'undefined' ? navigator.userAgent : 'Unknown';
    }
    return `${Platform.OS} ${Platform.Version}`;
  }

  /**
   * Get current URL (for web) or screen name
   */
  private getCurrentUrl(): string {
    if (Platform.OS === 'web') {
      return typeof window !== 'undefined' ? window.location.href : 'Unknown';
    }
    return 'Mobile App';
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }
}

export const simpleErrorService = new SimpleErrorService();

// Global error handler
export const setupGlobalErrorHandling = () => {
  // Handle unhandled promise rejections (web)
  if (typeof window !== 'undefined') {
    window.addEventListener('unhandledrejection', (event) => {
      simpleErrorService.reportError(
        new Error(`Unhandled Promise Rejection: ${event.reason}`),
        {
          component: 'Global',
          action: 'UnhandledPromiseRejection',
          severity: 'high',
          metadata: { reason: event.reason },
        }
      );
    });

    window.addEventListener('error', (event) => {
      simpleErrorService.reportError(event.error || new Error(event.message), {
        component: 'Global',
        action: 'UnhandledError',
        severity: 'critical',
        metadata: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        },
      });
    });
  }

  // React Native error handling would go here if needed
  // For now, we'll rely on the existing error boundary patterns
};

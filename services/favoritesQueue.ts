import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import { favoritesService, FavoriteContentType } from './favoritesService';
import { logger } from '../utils/logger';

export interface QueuedFavoriteOperation {
  id: string;
  userId: string;
  itemId: string;
  contentType: FavoriteContentType;
  operation: 'add' | 'remove' | 'update_metadata';
  metadata?: Record<string, any>;
  timestamp: number;
  retryCount: number;
}

const QUEUE_STORAGE_KEY = 'favorites_offline_queue';
const MAX_RETRY_ATTEMPTS = 3;
const RETRY_DELAY_BASE = 1000; // 1 second base delay

class FavoritesQueue {
  private queue: QueuedFavoriteOperation[] = [];
  private isProcessing = false;
  private listeners: Set<(queue: QueuedFavoriteOperation[]) => void> = new Set();

  constructor() {
    this.loadQueue();
    this.setupNetworkListener();
  }

  /**
   * Add operation to offline queue
   */
  async enqueue(operation: Omit<QueuedFavoriteOperation, 'id' | 'timestamp' | 'retryCount'>): Promise<void> {
    const queuedOperation: QueuedFavoriteOperation = {
      ...operation,
      id: `${operation.userId}-${operation.itemId}-${operation.contentType}-${Date.now()}`,
      timestamp: Date.now(),
      retryCount: 0,
    };

    this.queue.push(queuedOperation);
    await this.saveQueue();
    this.notifyListeners();

    logger.info(`Queued favorite operation: ${operation.operation} for ${operation.contentType} ${operation.itemId}`);

    // Try to process immediately if online
    const netInfo = await NetInfo.fetch();
    if (netInfo.isConnected) {
      this.processQueue();
    }
  }

  /**
   * Remove operation from queue
   */
  async dequeue(operationId: string): Promise<void> {
    this.queue = this.queue.filter(op => op.id !== operationId);
    await this.saveQueue();
    this.notifyListeners();
  }

  /**
   * Get current queue
   */
  getQueue(): QueuedFavoriteOperation[] {
    return [...this.queue];
  }

  /**
   * Subscribe to queue changes
   */
  subscribe(listener: (queue: QueuedFavoriteOperation[]) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  /**
   * Process all queued operations
   */
  async processQueue(): Promise<void> {
    if (this.isProcessing || this.queue.length === 0) {
      return;
    }

    const netInfo = await NetInfo.fetch();
    if (!netInfo.isConnected) {
      logger.info('No network connection, skipping queue processing');
      return;
    }

    this.isProcessing = true;
    logger.info(`Processing ${this.queue.length} queued favorite operations`);

    const operationsToProcess = [...this.queue];
    
    for (const operation of operationsToProcess) {
      try {
        await this.processOperation(operation);
        await this.dequeue(operation.id);
        logger.info(`Successfully processed queued operation: ${operation.id}`);
      } catch (error) {
        logger.error(`Failed to process queued operation ${operation.id}:`, error);
        await this.handleFailedOperation(operation, error);
      }
    }

    this.isProcessing = false;
  }

  /**
   * Clear all queued operations
   */
  async clearQueue(): Promise<void> {
    this.queue = [];
    await this.saveQueue();
    this.notifyListeners();
  }

  private async processOperation(operation: QueuedFavoriteOperation): Promise<void> {
    const { userId, itemId, contentType, operation: op, metadata } = operation;

    switch (op) {
      case 'add':
        await favoritesService.addFavorite({ user_id: userId, item_id: itemId, type: contentType, metadata });
        break;
      case 'remove':
        await favoritesService.removeFavorite(userId, itemId, contentType);
        break;
      case 'update_metadata':
        if (metadata) {
          await favoritesService.updateFavoriteMetadata(userId, itemId, contentType, metadata);
        }
        break;
      default:
        throw new Error(`Unknown operation: ${op}`);
    }
  }

  private async handleFailedOperation(operation: QueuedFavoriteOperation, error: any): Promise<void> {
    const updatedOperation = {
      ...operation,
      retryCount: operation.retryCount + 1,
    };

    if (updatedOperation.retryCount >= MAX_RETRY_ATTEMPTS) {
      logger.error(`Max retry attempts reached for operation ${operation.id}, removing from queue`);
      await this.dequeue(operation.id);
      return;
    }

    // Update the operation in the queue with new retry count
    const index = this.queue.findIndex(op => op.id === operation.id);
    if (index !== -1) {
      this.queue[index] = updatedOperation;
      await this.saveQueue();
      this.notifyListeners();
    }

    // Schedule retry with exponential backoff
    const delay = RETRY_DELAY_BASE * Math.pow(2, updatedOperation.retryCount - 1);
    setTimeout(() => {
      this.processQueue();
    }, delay);
  }

  private async loadQueue(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(QUEUE_STORAGE_KEY);
      if (stored) {
        this.queue = JSON.parse(stored);
        logger.info(`Loaded ${this.queue.length} queued favorite operations`);
      }
    } catch (error) {
      logger.error('Error loading favorites queue:', error);
      this.queue = [];
    }
  }

  private async saveQueue(): Promise<void> {
    try {
      await AsyncStorage.setItem(QUEUE_STORAGE_KEY, JSON.stringify(this.queue));
    } catch (error) {
      logger.error('Error saving favorites queue:', error);
    }
  }

  private setupNetworkListener(): void {
    NetInfo.addEventListener(state => {
      if (state.isConnected && this.queue.length > 0) {
        logger.info('Network connection restored, processing queued operations');
        this.processQueue();
      }
    });
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener([...this.queue]);
      } catch (error) {
        logger.error('Error in queue listener:', error);
      }
    });
  }
}

export const favoritesQueue = new FavoritesQueue();
export default favoritesQueue;

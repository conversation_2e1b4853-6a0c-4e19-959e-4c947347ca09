-- Migration: Couples and Pairing System
-- Creates tables and infrastructure for partner pairing and shared data

-- Enable necessary extensions
create extension if not exists "uuid-ossp";

-- Couples table for partner relationships
create table if not exists public.couples (
  id uuid not null default gen_random_uuid(),
  partner1_user_id uuid not null,
  partner2_user_id uuid null, -- null until partner joins
  couple_code text not null unique,
  qr_code_data jsonb null, -- QR code metadata and deep link info
  status text not null default 'pending', -- pending, active, expired, cancelled
  created_at timestamp with time zone not null default now(),
  expires_at timestamp with time zone not null default (now() + interval '7 days'),
  updated_at timestamp with time zone not null default now(),
  
  constraint couples_pkey primary key (id),
  constraint couples_partner1_fkey foreign key (partner1_user_id) references auth.users (id) on delete cascade,
  constraint couples_partner2_fkey foreign key (partner2_user_id) references auth.users (id) on delete cascade,
  constraint couples_status_check check (status in ('pending', 'active', 'expired', 'cancelled')),
  constraint couples_different_partners check (partner1_user_id != partner2_user_id)
);

-- Pairing attempts tracking for security
create table if not exists public.pairing_attempts (
  id uuid not null default gen_random_uuid(),
  user_id uuid not null,
  attempted_code text not null,
  couple_id uuid null, -- reference to couple if attempt was for valid code
  success boolean not null default false,
  ip_address inet null,
  user_agent text null,
  created_at timestamp with time zone not null default now(),
  
  constraint pairing_attempts_pkey primary key (id),
  constraint pairing_attempts_user_fkey foreign key (user_id) references auth.users (id) on delete cascade,
  constraint pairing_attempts_couple_fkey foreign key (couple_id) references public.couples (id) on delete set null
);

-- Couple shared data table for Our Story and other shared content
create table if not exists public.couple_stories (
  id uuid not null default gen_random_uuid(),
  couple_id uuid not null,
  story_data jsonb not null default '{}',
  completed_sections text[] not null default '{}',
  last_updated timestamp with time zone not null default now(),
  last_updated_by uuid null, -- which partner made the last update
  created_at timestamp with time zone not null default now(),
  
  constraint couple_stories_pkey primary key (id),
  constraint couple_stories_couple_fkey foreign key (couple_id) references public.couples (id) on delete cascade,
  constraint couple_stories_updated_by_fkey foreign key (last_updated_by) references auth.users (id) on delete set null,
  constraint couple_stories_one_per_couple unique (couple_id)
);

-- Indexes for performance
create index if not exists idx_couples_partner1 on public.couples (partner1_user_id);
create index if not exists idx_couples_partner2 on public.couples (partner2_user_id);
create index if not exists idx_couples_code on public.couples (couple_code);
create index if not exists idx_couples_status on public.couples (status);
create index if not exists idx_couples_expires_at on public.couples (expires_at);

create index if not exists idx_pairing_attempts_user on public.pairing_attempts (user_id);
create index if not exists idx_pairing_attempts_code on public.pairing_attempts (attempted_code);
create index if not exists idx_pairing_attempts_created_at on public.pairing_attempts (created_at);

create index if not exists idx_couple_stories_couple on public.couple_stories (couple_id);
create index if not exists idx_couple_stories_updated_by on public.couple_stories (last_updated_by);

-- Unique index for user events (prevents duplicate events)
create unique index if not exists ux_user_events_user_event 
  on public.user_events (user_id, event_name);

-- Function to generate secure couple codes
create or replace function generate_couple_code()
returns text
language plpgsql
security definer
as $$
declare
  code text;
  exists_check boolean;
begin
  loop
    -- Generate 8-character alphanumeric code (high entropy)
    code := upper(
      substr(
        encode(gen_random_bytes(6), 'base64'),
        1, 8
      )
    );
    
    -- Replace problematic characters for better UX
    code := replace(code, '0', 'A');
    code := replace(code, '1', 'B');
    code := replace(code, 'I', 'C');
    code := replace(code, 'O', 'D');
    code := replace(code, '+', 'E');
    code := replace(code, '/', 'F');
    code := replace(code, '=', 'G');
    
    -- Check if code already exists
    select exists(
      select 1 from public.couples 
      where couple_code = code and status != 'expired'
    ) into exists_check;
    
    exit when not exists_check;
  end loop;
  
  return code;
end;
$$;

-- Function to check pairing attempt limits
create or replace function check_pairing_attempts(user_uuid uuid)
returns boolean
language plpgsql
security definer
as $$
declare
  failed_attempts integer;
begin
  -- Count failed attempts for this user
  select count(*)
  from public.pairing_attempts
  where user_id = user_uuid 
    and success = false
  into failed_attempts;
  
  -- Return true if under limit (3 attempts max)
  return failed_attempts < 3;
end;
$$;

-- Function to expire old couples
create or replace function expire_old_couples()
returns void
language plpgsql
security definer
as $$
begin
  update public.couples
  set status = 'expired'
  where status = 'pending'
    and expires_at < now();
end;
$$;

-- Drop and recreate onboarding progress view (enhanced with partner invitation)
drop view if exists public.onboarding_progress;

create view public.onboarding_progress as
select
  user_id,
  bool_or(event_name = 'onboarding_started') as started,
  bool_or(event_name = 'onboarding_intro_viewed') as intro_viewed,
  bool_or(event_name = 'onboarding_partner_profile_set') as partner_done,
  bool_or(event_name = 'onboarding_partner_invited') as partner_invited,
  bool_or(event_name = 'onboarding_ritual_configured') as ritual_done,
  bool_or(event_name = 'onboarding_journal_icon_selected') as journal_done,
  bool_or(event_name = 'onboarding_completed') as completed,
  max(created_at) as last_event_at
from public.user_events
where event_name like 'onboarding_%'
group by user_id;

-- Row Level Security (RLS) policies

-- Couples table policies
alter table public.couples enable row level security;

create policy "Users can view their own couples"
  on public.couples for select
  using (auth.uid() = partner1_user_id or auth.uid() = partner2_user_id);

create policy "Users can create couples as partner1"
  on public.couples for insert
  with check (auth.uid() = partner1_user_id);

create policy "Users can update their own couples"
  on public.couples for update
  using (auth.uid() = partner1_user_id or auth.uid() = partner2_user_id);

-- Pairing attempts policies
alter table public.pairing_attempts enable row level security;

create policy "Users can view their own pairing attempts"
  on public.pairing_attempts for select
  using (auth.uid() = user_id);

create policy "Users can create their own pairing attempts"
  on public.pairing_attempts for insert
  with check (auth.uid() = user_id);

-- Couple stories policies
alter table public.couple_stories enable row level security;

create policy "Couple members can view their story"
  on public.couple_stories for select
  using (
    exists (
      select 1 from public.couples c
      where c.id = couple_id
        and (c.partner1_user_id = auth.uid() or c.partner2_user_id = auth.uid())
        and c.status = 'active'
    )
  );

create policy "Couple members can update their story"
  on public.couple_stories for all
  using (
    exists (
      select 1 from public.couples c
      where c.id = couple_id
        and (c.partner1_user_id = auth.uid() or c.partner2_user_id = auth.uid())
        and c.status = 'active'
    )
  );

-- Create a scheduled job to clean up expired couples (if pg_cron is available)
-- This would typically be set up separately in production
-- select cron.schedule('expire-couples', '0 2 * * *', 'select expire_old_couples();');

-- Grant necessary permissions
grant usage on schema public to authenticated;
grant all on public.couples to authenticated;
grant all on public.pairing_attempts to authenticated;
grant all on public.couple_stories to authenticated;
grant select on public.onboarding_progress to authenticated;
grant execute on function generate_couple_code() to authenticated;
grant execute on function check_pairing_attempts(uuid) to authenticated;
grant execute on function expire_old_couples() to authenticated;

-- Insert some test data for development (remove in production)
-- insert into public.couples (partner1_user_id, couple_code, status) 
-- values ('00000000-0000-0000-0000-000000000000', 'TEST1234', 'pending');
